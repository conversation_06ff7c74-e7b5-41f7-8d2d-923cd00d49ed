import 'dart:ui' as ui;

import 'package:flutter/material.dart';

class TickMarkThumb extends SliderTickMarkShape {
  TickMarkThumb();

  @override
  ui.Size getPreferredSize({required SliderThemeData sliderTheme, required bool isEnabled}) {
    return Size.fromRadius(sliderTheme.trackHeight! / 1.5);
  }

  @override
  void paint(PaintingContext context, ui.Offset center,
      {required RenderBox parentBox,
      required SliderThemeData sliderTheme,
      required Animation<double> enableAnimation,
      required ui.Offset thumbCenter,
      required bool isEnabled,
      required ui.TextDirection textDirection}) {
    Paint circlePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    Paint borderPaint = Paint()
      ..color = Color(0xFFD9D9D9)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    final double tickMarkRadius = getPreferredSize(
          isEnabled: isEnabled,
          sliderTheme: sliderTheme,
        ).width /
        2;
    context.canvas.drawCircle(center, tickMarkRadius, circlePaint);
    context.canvas.drawCircle(center, tickMarkRadius, borderPaint);
  }
}

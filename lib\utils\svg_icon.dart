import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SvgIcon extends StatelessWidget {
  final String asset;
  final String? package;
  final Color? color;
  final double? side;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final bool matchTextDirection;

  const SvgIcon(this.asset,
      {Key? key,
      this.package,
      this.color,
      this.side,
      this.width,
      this.height,
      this.fit,
      this.matchTextDirection = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    double? _width = side ?? width;
    double? _height = side ?? height;
    return SizedBox(
      width: _width,
      height: _height,
      child: SvgPicture.asset(
        asset,
        package: package ?? 'niimbot_print_setting_plugin',
        color: color,
        fit: fit ?? BoxFit.none,
        matchTextDirection: matchTextDirection,
      ),
    );
  }
}

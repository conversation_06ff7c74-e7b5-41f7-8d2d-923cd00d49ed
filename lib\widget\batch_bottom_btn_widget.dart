import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/dialog_util.dart';

class BatchBottomBtnWidget extends StatefulWidget {
  PrintSettingLogic logic;

  BatchBottomBtnWidget(this.logic, {Key? key});

  @override
  State<BatchBottomBtnWidget> createState() => _BatchBottomBtnWidgetState(logic);
}

class _BatchBottomBtnWidgetState extends State<BatchBottomBtnWidget> {
  PrintSettingLogic logic;

  _BatchBottomBtnWidgetState(this.logic);

  static double bottomHeight = 0;

  @override
  void initState() {
    super.initState();
    // 检查批处理打印源的状态，并根据结果更新按钮状态
    logic.channel.checkBatchPrintSources(logic.parameter!.batchtIds!).then((value) {
      // 设置云资源下载成功状态
      logic.style.isDownloadSuccess = value == 1;
      _refreshButton();
    });

    // 监听逻辑流中的数据，以刷新打印资源状态
    logic.stream.listen((data) {
      if (data["action"] == "refreshBatchPrintRourcesStatus") {
        logic.channel.checkBatchPrintSources(logic.parameter!.batchtIds!).then((value) {
          logic.style.isDownloadSuccess = value == 1;
          _refreshButton();
          if (!logic.style.isDownloadSuccess) {
            downloadErrorDialog(context);
          }
        });
      }
    });
    WidgetsBinding.instance.endOfFrame.then(
      (value) {
        if (mounted) {
          bottomHeight = MediaQuery.of(context).padding.bottom;
        }
      },
    );

    if (logic.parameter!.batchtIds!.length > 1 || (logic.parameter!.batchtIds![0]["printPage"] as int) > 1) {
      logic.channel.trackEvent("show", "024_067_095");
    }
  }

  _refreshButton() {
    // 根据云资源下载是否成功，设置打印按钮的标题
    logic.style.printBtnTitle = logic.style.isDownloadSuccess
        ? logic.getI18nString!("app00016", "打印")
        : logic.getI18nString!("app100001260", "云资源下载中...");
    // 更新打印按钮的UI状态
    logic.update([UpdateFieldManager.printButton]);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.printButton,
        builder: (logic) {
          return Container(
            padding: EdgeInsetsDirectional.only(bottom: bottomHeight),
            margin: EdgeInsets.only(top: MediaQuery.of(context).viewInsets.bottom / 1.2),
            color: Colors.white,
            child: Column(
              children: [
                Divider(
                  height: 1,
                  thickness: 0.5,
                  color: Color(0xFFEBEBEB),
                ),
                SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          logic.channel.checkBatchPrintSources(logic.parameter!.batchtIds!).then((value) {
                            logic.style.isDownloadSuccess = value == 1;
                            if (!logic.style.isDownloadSuccess) {
                              downloadErrorDialog(context);
                            } else {
                              logic.toPrint();
                            }
                          });
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44,
                          decoration: BoxDecoration(
                            color: logic.style.isDownloadSuccess
                                ? logic.style.printBtnBgColor
                                : logic.style.PrintingBtnBgColor,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          margin: EdgeInsetsDirectional.symmetric(horizontal: 16),
                          child: logic.style.isPrinting
                              ? Container(
                                  alignment: Alignment.center,
                                  child: const CupertinoActivityIndicator(
                                    radius: 13,
                                    color: Colors.white,
                                    animating: true,
                                  ),
                                )
                              : Text(
                                  logic.style.printBtnTitle,
                                  style: logic.style.printBtnStyle,
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                ((logic.parameter!.batchtIds!.length > 1 || (logic.parameter!.batchtIds![0]["printPage"] as int) > 1) &&
                        logic.style.isDownloadSuccess)
                    ? GestureDetector(
                        onTap: () {
                          logic.channel.checkBatchPrintSources(logic.parameter!.batchtIds!).then((value) {
                            logic.style.isDownloadSuccess = value == 1;
                            if (!logic.style.isDownloadSuccess) {
                              downloadErrorDialog(context);
                            } else {
                              logic.toPrint(isJustOne: true);
                            }
                          });
                        },
                        child: Container(
                          child: Text(
                            logic.style.printJustOneBtnTitle,
                            style: logic.style.printJustOneBtnStyle,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    : Container(),
                Container(
                  height: bottomHeight > 0 ? 0 : 20,
                )
              ],
            ),
          );
        });
  }

  downloadErrorDialog(BuildContext context) {
    DialogUtil().showCustomDialog(context, "", logic.getI18nString("app100001270", "下载失败，请检查网络或稍后重试"),
        contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
        leftFunStr: logic.getI18nString("app00030", "取消"),
        rightFunStr: logic.getI18nString("app100001271", "重试"), rightFunCall: () {
      logic.channel.downloadTemplateDetails(logic.parameter!.batchtIds!);
    }, leftFunCall: () {
      logic.style.isDownloadSuccess = true;
      _refreshButton();
    }, channel: logic.channel);
  }
}

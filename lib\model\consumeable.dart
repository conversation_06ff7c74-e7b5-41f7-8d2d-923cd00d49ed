/// childProperties : [{"blindZone":"0.0|0.0|0.0|0.0","checked":false,"code":"1","id":0,"name":"间隙纸"},{"blindZone":"0.0|0.0|0.0|0.0","checked":false,"code":"2","id":0,"name":"黑标纸"},{"blindZone":"0.0|0.0|0.0|0.0","checked":false,"code":"3","id":0,"name":"连续纸"},{"blindZone":"0.0|0.0|0.0|0.0","checked":false,"code":"5","id":0,"name":"透明纸"}]
/// parentProperty : {"checked":false,"code":"","density":3,"id":0,"name":"旧版","printModeValue":1}

class Consumeable {
  Consumeable({
    List<ChildProperties>? childProperties,
    ParentProperty? parentProperty,
  }) {
    _childProperties = childProperties;
    _parentProperty = parentProperty;
  }

  Consumeable.fromJson(dynamic json) {
    if (json['childProperties'] != null) {
      _childProperties = [];
      json['childProperties'].forEach((v) {
        _childProperties?.add(ChildProperties.fromJson(v));
      });
    }
    _parentProperty = json['parentProperty'] != null ? ParentProperty.fromJson(json['parentProperty']) : null;
  }
  List<ChildProperties>? _childProperties;
  ParentProperty? _parentProperty;
  Consumeable copyWith({
    List<ChildProperties>? childProperties,
    ParentProperty? parentProperty,
  }) =>
      Consumeable(
        childProperties: childProperties ?? _childProperties,
        parentProperty: parentProperty ?? _parentProperty,
      );
  List<ChildProperties>? get childProperties => _childProperties;
  ParentProperty? get parentProperty => _parentProperty;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_childProperties != null) {
      map['childProperties'] = _childProperties?.map((v) => v.toJson()).toList();
    }
    if (_parentProperty != null) {
      map['parentProperty'] = _parentProperty?.toJson();
    }
    return map;
  }
}

/// checked : false
/// code : ""
/// density : 3
/// id : 0
/// name : "旧版"
/// printModeValue : 1

class ParentProperty {
  ParentProperty({
    bool? checked,
    String? code,
    num? density,
    num? id,
    String? name,
    num? printModeValue,
  }) {
    _checked = checked;
    _code = code;
    _density = density;
    _id = id;
    _name = name;
    _printModeValue = printModeValue;
  }

  ParentProperty.fromJson(dynamic json) {
    if (json['printModeValue'] == 'null') {
      json['printModeValue'] = 0;
    }
    _checked = json['checked'];
    _code = json['code'].toString();
    _density = json['density'];
    _id = json['id'];
    _name = json['name'];
    _printModeValue = json['printModeValue'] is String ? num.parse(json['printModeValue']) : json['printModeValue'];
  }
  bool? _checked;
  String? _code;
  num? _density;
  num? _id;
  String? _name;
  num? _printModeValue;
  ParentProperty copyWith({
    bool? checked,
    String? code,
    num? density,
    num? id,
    String? name,
    num? printModeValue,
  }) =>
      ParentProperty(
        checked: checked ?? _checked,
        code: code ?? _code,
        density: density ?? _density,
        id: id ?? _id,
        name: name ?? _name,
        printModeValue: printModeValue ?? _printModeValue,
      );
  bool? get checked => _checked;
  String? get code => _code;
  num? get density => _density;
  num? get id => _id;
  String? get name => _name;
  num? get printModeValue => _printModeValue;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['checked'] = _checked;
    map['code'] = _code;
    map['density'] = _density;
    map['id'] = _id;
    map['name'] = _name;
    map['printModeValue'] = _printModeValue;
    return map;
  }
}

/// blindZone : "0.0|0.0|0.0|0.0"
/// checked : false
/// code : "1"
/// id : 0
/// name : "间隙纸"

class ChildProperties {
  ChildProperties({
    String? blindZone,
    bool? checked,
    String? code,
    num? id,
    String? name,
  }) {
    _blindZone = blindZone;
    _checked = checked;
    _code = code;
    _id = id;
    _name = name;
  }

  ChildProperties.fromJson(dynamic json) {
    _blindZone = json['blindZone'];
    _checked = json['checked'];
    _code = json['code'].toString();
    _id = json['id'];
    _name = json['name'];
  }
  String? _blindZone;
  bool? _checked;
  String? _code;
  num? _id;
  String? _name;
  ChildProperties copyWith({
    String? blindZone,
    bool? checked,
    String? code,
    num? id,
    String? name,
  }) =>
      ChildProperties(
        blindZone: blindZone ?? _blindZone,
        checked: checked ?? _checked,
        code: code ?? _code,
        id: id ?? _id,
        name: name ?? _name,
      );
  String? get blindZone => _blindZone;
  bool? get checked => _checked;
  String? get code => _code;
  num? get id => _id;
  String? get name => _name;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['blindZone'] = _blindZone;
    map['checked'] = _checked;
    map['code'] = _code;
    map['id'] = _id;
    map['name'] = _name;
    return map;
  }
}

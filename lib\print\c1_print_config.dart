import 'dart:ui';

import 'package:nety/models/index.dart';
import 'package:niimbot_print_setting_plugin/print/print_config.dart';

class C1PrintConfig extends PrintConfig {
  /// 切割类型(C1线号机使用)  0 - 不切  1 - 分割线  2 - 半切
  int? cutType;

  /// 耗材类型（C1线号机使用）
  int? tubeType;

  /// 耗材类型的直径（C1线号机使用）
  double? tubeSpecs;

  /// 是否半切
  bool? isHalfCut;

  /// 半切深度
  int? cutDepth;

  C1PrintConfig(
      {int? copies,
      required int density,
      required List<int> densityRange,
      required int originalDensity,
      Offset? printOffset,
      int? pageTotal,
      int? total,
      int? page,
      NiimbotPrintMode? printMode,
      NiimbotPrintQualityMode? qualityMode,
      NiimbotPrintColorMode? colorMode,
      this.cutType,
      this.tubeType,
      this.tubeSpecs,
      this.isHalfCut,
      this.cutDepth})
      : super(
            copies: copies,
            density: density,
            densityRange: densityRange,
            originalDensity: originalDensity,
            printOffset: printOffset,
            pageTotal: pageTotal,
            total: total,
            page: page,
            printMode: printMode,
            qualityMode: qualityMode,
            colorMode: colorMode);
}

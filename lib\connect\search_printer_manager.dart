import 'package:nety/models/index.dart';
import 'package:nety/models/niimbot_bluetooth_printer.dart';
import 'package:nety/models/niimbot_wifi_printer.dart';
import 'package:nety/nety.dart';
import 'package:nety/store.dart';
import 'package:niimbot_print_setting_plugin/extensions/change_notifer.dart';

class SearchPrinterManager {
  //回调搜索到蓝牙打印机事件
  // Function(List<NiimbotBluetoothPrinter?>?)? bluetoothPrinterFoundCallback;
  Function(NiimbotBluetoothPrinter)? bluetoothPrinterFoundCallback;

  //回调搜索到wifi打印机事件
  Function(NiimbotWIFIPrinter)? wifiPrinterFoundCallback;

  //回调蓝牙搜索过程结束事件
  Function()? bluetoothDiscoverFinishedCallback;

  //回调wifi搜索过程结束事件
  Function()? wifiDiscoverFinishedCallback;

  //监听nety插件蓝牙搜索状态变化
  Function()? _discoverBluetoothStatusListener;

  //监听nety插件搜索到蓝牙打印机
  Function()? _discoverBluetoothPrinterListener;

  //监听nety插件wifi搜索状态变化
  Function()? _discoverWifiStatusListener;

  //监听nety插件搜索到wifi打印机
  Function()? _discoverWifiPrinterListener;
  static SearchPrinterManager? _instance;

  // Avoid self instance
  SearchPrinterManager._();

  factory SearchPrinterManager() {
    _instance ??= SearchPrinterManager._();
    return _instance!;
  }

  /// 同时搜索蓝牙打印机和wifi打印机
  Future<void> discoverPrinter() async {
    await NiimbotPrintSDK().discoverPrinter(types: {NiimbotPrinterType.bluetooth, NiimbotPrinterType.wifi});
    _setBluetoothDiscoverListener();
    _setWifiDiscoverListener();
  }

  /// 搜索蓝牙打印机
  Future<void> discoverBluetoothPrinter() async {
    await NiimbotPrintSDK().discoverPrinter(types: {NiimbotPrinterType.bluetooth});
    _setBluetoothDiscoverListener();
  }

  /// 搜索wifi打印机
  Future<void> discoverWIFIPrinter() async {
    await NiimbotPrintSDK().discoverPrinter(types: {NiimbotPrinterType.wifi});
    _setWifiDiscoverListener();
  }

  /// 停止蓝牙搜索和wifi搜索
  Future<void> stopDiscoverPrinter() async {
    _removeBluetoothDiscoverListener();
    _removeWifiDiscoverListener();
    await NiimbotPrintSDK().stopDiscoverPrinter();
  }

  /// 停止蓝牙搜索
  Future<int> stopDiscoverBluetoothPrinter() async {
    _removeBluetoothDiscoverListener();
    return await NiimbotPrintSDK().stopDiscoverBluetoothPrinter();
  }

  /// 停止wifi搜索
  Future<int> stopDiscoverWIFIPrinter() async {
    _removeWifiDiscoverListener();
    return await NiimbotPrintSDK().stopDiscoverWIFIPrinter();
  }

  _setBluetoothDiscoverListener() {
    // //watch返回值为移除监听事件Function
    // _discoverBluetoothPrinterListener ??=
    //     NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, List<NiimbotPrinter>>((v) => v.printers, (oldVal, newVal) {
    //   // 监听搜索到的蓝牙设备和wifi设备
    //   List<NiimbotBluetoothPrinter?>? niimbotPrinters = newVal
    //       .where((newPrinter) {
    //         // 筛选出出老的中已返回的
    //         if (oldVal.none((oldPrinter) => oldPrinter.isSame(newPrinter)) &&
    //             newPrinter.type == NiimbotPrinterType.bluetooth) {
    //           return true;
    //         }
    //         return false;
    //       })
    //       .map((e) => e as NiimbotBluetoothPrinter?)
    //       .toList();
    //   bluetoothPrinterFoundCallback?.call(niimbotPrinters);
    // });
    NiimbotPrintSDK.bluetoothPrinterFound = (bluetoothPrinter) {
      bluetoothPrinterFoundCallback?.call(bluetoothPrinter);
    };
    //watch返回值为移除监听事件Function
    _discoverBluetoothStatusListener ??=
        NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, bool>((v) => v.bluetoothDiscovering, (oldVal, newVal) {
      //监听搜索状态变化
      if (oldVal && !newVal) {
        _removeBluetoothDiscoverListener();
        bluetoothDiscoverFinishedCallback?.call();
      }
    });
  }

  _removeBluetoothDiscoverListener() {
    // _discoverBluetoothPrinterListener?.call();
    // _discoverBluetoothPrinterListener = null;
    NiimbotPrintSDK.bluetoothPrinterFound = null;
    _discoverBluetoothStatusListener?.call();
    _discoverBluetoothStatusListener = null;
  }

  _setWifiDiscoverListener() {
    // //watch返回值为移除监听事件Function
    // _discoverWifiPrinterListener ??=
    //     NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, List<NiimbotPrinter>>((v) => v.printers, (oldVal, newVal) {
    //   //监听搜索到的蓝牙设备和wifi设备
    //   NiimbotPrinter? niimbotPrinter =
    //       newVal.firstWhereOrNull((newPrinter) => oldVal.none((oldPrinter) => oldPrinter.isSame(newPrinter)));
    //   if (niimbotPrinter?.type != NiimbotPrinterType.wifi) {
    //     return;
    //   }
    //   NiimbotWIFIPrinter wifiPrinter = niimbotPrinter as NiimbotWIFIPrinter;
    //   wifiPrinterFoundCallback?.call(wifiPrinter);
    // });
    NiimbotPrintSDK.wifiPrinterFound = (wifiPrinter) {
      wifiPrinterFoundCallback?.call(wifiPrinter);
    };
    //watch返回值为移除监听事件Function
    _discoverWifiStatusListener ??=
        NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, bool>((v) => v.wifiDiscovering, (oldVal, newVal) {
      //监听搜索状态变化
      if (oldVal && !newVal) {
        _removeWifiDiscoverListener();
        wifiDiscoverFinishedCallback?.call();
      }
    });
  }

  _removeWifiDiscoverListener() {
    // _discoverWifiPrinterListener?.call();
    // _discoverWifiPrinterListener = null;
    NiimbotPrintSDK.wifiPrinterFound = null;
    _discoverWifiStatusListener?.call();
    _discoverWifiStatusListener = null;
  }
}

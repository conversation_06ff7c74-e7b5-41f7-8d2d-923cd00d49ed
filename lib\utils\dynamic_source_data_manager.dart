import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:path_provider/path_provider.dart';

import 'dynamic_source_data.dart';

/// 动态数据源 内容管理
class DynamicSourceDataManager {
  static const String Dynamic_Source_File_Directory = "/dynamic_source";

  factory DynamicSourceDataManager() => sharedInstance();

  static DynamicSourceDataManager? _dataManager;

  static DynamicSourceDataManager sharedInstance() {
    if (_dataManager == null) {
      _dataManager = new DynamicSourceDataManager._internal();
    }
    return _dataManager!;
  }

  DynamicSourceDataManager._internal();

  /// Excel文件缓存到本地文件
  Future cacheDynamicSourceData(DynamicSourceData dynamicSourceData) async {
    String path = await getDynamicSourceDataPath(dynamicSourceData.hash);
    File file = File(path);
    if (file.existsSync()) {
      return;
    }
    await file.create(recursive: true);
    String content = json.encode(dynamicSourceData.toJson());
    await file.writeAsString(content);
  }

  /// 从本地文件读取Excel文件内容详情
  Future<DynamicSourceData?> readDynamicSourceData(String hash) async {
    String path = await getDynamicSourceDataPath(hash);
    File file = File(path);
    if (!file.existsSync()) {
      return null;
    }
    String content = await file.readAsString();
    if (content.isEmpty) {
      return null;
    }
    return DynamicSourceData.fromJson(json.decode(content));
  }

  /// Excel文件内容详情本地保存路径
  Future<String> getDynamicSourceDataPath(String hash) async {
    String localFileName = "${hash}.json";
    return (await getApplicationDocumentsDirectory()).path +
        DynamicSourceDataManager.Dynamic_Source_File_Directory +
        "/$localFileName";
  }
}

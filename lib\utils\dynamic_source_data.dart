/// 动态数据源内容
class DynamicSourceData {
  /// 列头数组
  List<String> headers;

  /// excel内容二维数据：一维为行数，二维为当前行数据
  List<List<String>> rowData;

  ///excel表名
  String sheetName;

  String hash;

  DynamicSourceData({
    required this.headers,
    required this.rowData,
    required this.sheetName,
    required this.hash,
  });

  factory DynamicSourceData.fromJson(Map<String, dynamic> json) {
    List<dynamic> headersJson = json['headers'] ?? [];

    List<String> headers = headersJson.map((e) => e.toString()).toList();

    List<dynamic> rowDataJson = json['rowData'] ?? [];

    List<List<String>> rowData = rowDataJson.map((e) {
      List<dynamic> rowJson = e ?? [];
      return rowJson.map((e) => e.toString()).toList();
    }).toList();

    String sheetName = json['sheetName'] ?? '';
    String hash = json['hash'] ?? '';

    return DynamicSourceData(
        headers: headers, rowData: rowData, sheetName: sheetName, hash: hash);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['headers'] = this.headers.map((e) => e.toString()).toList();
    data['rowData'] =
        this.rowData.map((e) => e.map((e) => e.toString()).toList()).toList();
    data['sheetName'] = this.sheetName;
    data['hash'] = this.hash;
    return data;
  }
}

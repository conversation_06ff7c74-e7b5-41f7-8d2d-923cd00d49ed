import 'dart:convert';

import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/model/consumeable.dart';

class DrawIntentParams {
  static const int PRINTER_ALIGN_TYPE_CENTER = 2;
  static const int PRINTER_ALIGN_TYPE_LEFT = 1;
  static const int PRINTER_ALIGN_TYPE_RIGHT = 3;
}

class TemplateArea {
  final num rotate;
  final num width;
  final num height;
  final num cableLength;
  final int cableDirection;
  final num canvasRotate;

  TemplateArea({
    required this.rotate,
    required this.width,
    required this.height,
    required this.cableLength,
    required this.cableDirection,
    required this.canvasRotate,
  });
}

class PrintUtils {
  static List<double> marginInfo = [0.0, 0.0, 0.0, 0.0];

  static List<int> getPrintArea(TemplateArea t, int canvasWidth, Map deviceMap, Map<String, dynamic>? templateMoudle,
      {bool isForPrint = false}) {
    var printAlign = (((deviceMap["printMethodCode"] ?? 0) is String)
        ? int.parse(deviceMap["printMethodCode"])
        : (deviceMap["printMethodCode"] ?? 0)) as int;
    var widthSetEnd = (((deviceMap["widthSetEnd"] ?? 0) is String)
        ? int.parse(deviceMap["widthSetEnd"])
        : (deviceMap["widthSetEnd"] ?? 0)) as int;
    List<int> printRect = List.filled(4, 0); // left|right|top|bottom
    handleMargin(jsonEncode(deviceMap['consumables']), templateMoudle?['consumableType'], templateMoudle?["paperType"]);
    int? printerMaxSize = widthSetEnd ?? 0;
    if (printerMaxSize <= 0) {
      return printRect;
    }
    //int width = (t.rotate == 90 || t.rotate == 270) ? t.height.toInt() : t.width.toInt();

    bool checkCable = false;
    int width;

    if (t.rotate == 90 || t.rotate == 270) {
      if (t.cableLength > 0 && (t.cableDirection == 0 || t.cableDirection == 2)) {
        checkCable = true;
        width = (t.height + t.cableLength).toInt();
      } else {
        width = t.height.toInt();
      }
    } else {
      if (t.cableLength > 0 && (t.cableDirection == 1 || t.cableDirection == 3)) {
        checkCable = true;
        width = (t.width + t.cableLength).toInt();
      } else {
        width = t.width.toInt();
      }
    }

    double rate = isForPrint ? 1 : canvasWidth / t.width;
    List<int> tempRect;
    switch (printAlign) {
      case DrawIntentParams.PRINTER_ALIGN_TYPE_CENTER:
        tempRect = centerPrint(printerMaxSize, width, rate, t, checkCable);
        break;
      case DrawIntentParams.PRINTER_ALIGN_TYPE_LEFT:
        tempRect = leftPrint(printerMaxSize, width, rate, t, checkCable);
        break;
      case DrawIntentParams.PRINTER_ALIGN_TYPE_RIGHT:
        tempRect = rightPrint(printerMaxSize, width, rate, t, checkCable);
        break;
      default:
        tempRect = centerPrint(printerMaxSize, width, rate, t, checkCable);
        break;
    }
    switch (t.rotate) {
      case 90:
        printRect[2] = tempRect[1];
        printRect[0] = tempRect[2];
        printRect[3] = tempRect[0];
        printRect[1] = tempRect[3];
        break;
      case 180:
        printRect[2] = tempRect[3];
        printRect[0] = tempRect[1];
        printRect[3] = tempRect[2];
        printRect[1] = tempRect[0];
        break;
      case 270:
        printRect[2] = tempRect[0];
        printRect[0] = tempRect[3];
        printRect[3] = tempRect[1];
        printRect[1] = tempRect[2];
        break;
      default:
        printRect = tempRect;
        break;
    }
    return printRect;
  }

  static void handleMargin(consumables, consumableType, paperType) {
    // 初始化 marginInfo 数组
    marginInfo = List.filled(4, 0.0);
    List<dynamic> jsonList = jsonDecode(consumables); // 解码 JSON 数据为 List<dynamic>

    List<Consumeable> consumablesList =
        jsonList.map((json) => Consumeable.fromJson(json as Map<String, dynamic>)).toList(); // 映射到 List<Consumeable>

    // 获取盲区字符串
    // String? marginStr = RFIDConnectionProxyManager.connectedDevice?.getBlindZoneByChoice(
    //   templateModuleLocal.consumableType,
    //   templateModuleLocal.paperType,
    // );
    //
    // consumables.firstWhere((test))
    var marginStr = consumablesList
        .firstWhereOrNull((value) => value.parentProperty?.code == consumableType.toString())
        ?.childProperties
        ?.firstWhereOrNull((t) => t.code == paperType.toString())
        ?.blindZone;
    if (marginStr != null && marginStr.isNotEmpty) {
      try {
        // 分割字符串并解析为 marginInfo 数组
        List<String> marginArr = marginStr.split("|");
        marginInfo[0] = marginArr.length > 0 ? double.tryParse(marginArr[0]) ?? 0.0 : 0.0;
        marginInfo[1] = marginArr.length > 1 ? double.tryParse(marginArr[1]) ?? 0.0 : 0.0;
        marginInfo[2] = marginArr.length > 2 ? double.tryParse(marginArr[2]) ?? 0.0 : 0.0;
        marginInfo[3] = marginArr.length > 3 ? double.tryParse(marginArr[3]) ?? 0.0 : 0.0;
      } catch (e) {
        // 处理异常
      }
    }
  }

  static List<int> centerPrint(int printerMaxWidth, int moduleWidth, double rate, TemplateArea t, bool checkCable) {
    int center = moduleWidth - printerMaxWidth < 0 ? 0 : moduleWidth - printerMaxWidth;
    int left = ((center / 2.0 + getLeftMargin()) * rate).toInt();
    int right = ((center / 2.0 + getRightMargin()) * rate).toInt();

    if (t.cableLength > 0 && t.cableDirection >= 0 && checkCable) {
      // 画板的旋转方向会影响尾巴方向，所以需要还原尾巴方向
      int cableDirection = (4 + t.cableDirection - t.canvasRotate ~/ 90) % 4;
      if (cableDirection == 1) {
        right = 0;
      } else {
        left = 0;
      }
    }

    int top = (getTopMargin() * rate).toInt();
    int bottom = (getBottomMargin() * rate).toInt();
    return [left, right, top, bottom];
  }

  static List<int> leftPrint(int printerMaxWidth, int moduleWidth, double rate, TemplateArea t, bool checkCable) {
    int left = (getLeftMargin() * rate).toInt();
    int right = moduleWidth - printerMaxWidth < 0
        ? (getRightMargin() * rate).toInt()
        : ((moduleWidth - printerMaxWidth + getRightMargin()) * rate).toInt();

    if (t.cableLength > 0 && t.cableDirection >= 0 && checkCable) {
      // 画板的旋转方向会影响尾巴方向，所以需要还原尾巴方向
      int cableDirection = (4 + t.cableDirection - t.canvasRotate ~/ 90) % 4;
      if (cableDirection == 1) {
        right = 0;
      } else {
        left = 0;
      }
    }

    int top = (getTopMargin() * rate).toInt();
    int bottom = (getBottomMargin() * rate).toInt();
    return [left, right, top, bottom];
  }

  static List<int> rightPrint(int printerMaxWidth, int moduleWidth, double rate, TemplateArea t, bool checkCable) {
    int right = (getRightMargin() * rate).toInt();
    int left = moduleWidth - printerMaxWidth < 0
        ? (getLeftMargin() * rate).toInt()
        : ((moduleWidth - printerMaxWidth + getLeftMargin()) * rate).toInt();

    if (t.cableLength > 0 && t.cableDirection >= 0 && checkCable) {
      // 画板的旋转方向会影响尾巴方向，所以需要还原尾巴方向
      int cableDirection = (4 + t.cableDirection - t.canvasRotate ~/ 90) % 4;
      if (cableDirection == 1) {
        right = 0;
      } else {
        left = 0;
      }
    }

    int top = (getTopMargin() * rate).toInt();
    int bottom = (getBottomMargin() * rate).toInt();
    return [left, right, top, bottom];
  }

  static double getLeftMargin() {
    return marginInfo[2];
  }

  static double getRightMargin() {
    return marginInfo[3];
  }

  static double getTopMargin() {
    return marginInfo[0];
  }

  static double getBottomMargin() {
    return marginInfo[1];
  }
}

#ifndef FLUTTER_PLUGIN_NIIMBOT_PRINT_SETTING_PLUGIN_H_
#define FLUTTER_PLUGIN_NIIMBOT_PRINT_SETTING_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace niimbot_print_setting_plugin {

class NiimbotPrintSettingPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  NiimbotPrintSettingPlugin();

  virtual ~NiimbotPrintSettingPlugin();

  // Disallow copy and assign.
  NiimbotPrintSettingPlugin(const NiimbotPrintSettingPlugin&) = delete;
  NiimbotPrintSettingPlugin& operator=(const NiimbotPrintSettingPlugin&) = delete;

 private:
  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace niimbot_print_setting_plugin

#endif  // FLUTTER_PLUGIN_NIIMBOT_PRINT_SETTING_PLUGIN_H_

/// 定义一个数据源类，用于统一描述不同数据的来源
class DataSource {
  /// 数据源的类型，如文件、网络等
  String type = '';

  /// 数据源的统一资源标识符，具体含义取决于类型
  String uri = '';

  /// 数据源的哈希值，用于校验数据的一致性，可选
  String? hash;

  /// 数据源的友好名称，可选
  String? name;

  /// 数据源的字节范围，用于支持断点续传等功能，可选
  List<Range>? range;

  /// 请求头信息，用于网络请求时附加的自定义头信息，可选
  Map<String, dynamic>? headers;

  /// 请求参数，用于网络请求时附加的自定义参数，可选
  Map<String, dynamic>? params;

  /// 默认构造函数，用于实例化数据源对象
  DataSource();
}

/// 定义一个字节范围类，用于描述数据的字节范围
class Range {
  /// 范围的起始位置，必填
  int s = 0;

  /// 范围的结束位置，必填
  int e = 0;

  /// 构造函数，用于实例化字节范围对象
  /// [s] - 范围的起始位置
  /// [e] - 范围的结束位置
  Range({required this.s, required this.e});
}

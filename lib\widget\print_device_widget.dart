import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/FToast.dart';
import 'package:niimbot_print_setting_plugin/utils/dialog_util.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';
import 'package:niimbot_print_setting_plugin/widget/print_offset_widget.dart';

class PrintDeviceWidget extends StatefulWidget {
  PrintSettingLogic logic;

  PrintDeviceWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintDeviceWidgetState createState() => _PrintDeviceWidgetState(logic);
}

class _PrintDeviceWidgetState extends State<PrintDeviceWidget> {
  PrintSettingLogic logic;
  bool deviceShow = false;
  List deviceMsgList = [];

  _PrintDeviceWidgetState(this.logic);

  @override
  void initState() {
    super.initState();

    /// 异步获取设备信息和打印设置
    getDevice() async {
      // 获取已连接设备的信息
      var value = await logic.channel.getConnectDevice();
      Map<String, dynamic> offsetInfo = await logic.getDeviceOffset();
      if (value != null) {
        logic.style.isConnectDevice = value["connected"] == 1;
        // 更新UI层的连接标题和打印数据的偏移量
        logic.style.connectTitle = value["machineName"] ?? "";
        logic.printData.ofsetX = offsetInfo["offsetX"] ?? 0;
        logic.printData.ofsetY = offsetInfo["offsetY"] ?? 0;
        logic.style.isWifi = value["isWifi"] ?? 0;
        bool isCable = false;
        logic.style.isCarbon = (value["isCarbon"] is int) ? value["isCarbon"] == 1 : (value["isCarbon"] ?? false); //碳带
        logic.style.carbonColor = value["carbonColors"] ?? "";
        logic.update([UpdateFieldManager.preview]);
      }
      // 获取打印设置消息，根据当前材料模型的序列号
      var list = await logic.channel.getPrintSettingMsg(logic.extrain['materialModelSn']);

      // 将获取的设备消息列表转换为List并赋值给deviceMsgList
      deviceMsgList = List.from(list);
      if (logic.style.isOffsetConnect) {
        logic.style.isOffsetConnect = false;
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          isDismissible: true,
          enableDrag: false,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
          builder: (BuildContext context) {
            // 返回 PrintSlowPage 作为底部导航的内容
            return PrintOffsetWidget(logic);
          },
        );
      }
      // 通知更新设备界面
      logic.update([UpdateFieldManager.device]);
    }

    // 调用getDevice函数以初始化设备信息
    getDevice.call();

    // 监听来自logic的流，以响应打印机连接状态的变化
    logic.stream.listen((data) {
      // 当动作是"printerConnectState"时，表示打印机连接状态有变化
      if (data["action"] == "printerConnectState") {
        // 延迟1秒后重新调用getDevice函数以更新设备信息，确保最新数据
        logic.style.isConnectDevice = data["printerConnectState"]["connected"] == 1;
        Future.delayed(Duration(milliseconds: 2000), () {
          getDevice.call();
        });
      } else if (data["action"] == "refreshTemplate" || data["action"] == "currentPrintColor" || data["action"] == "loginStateChanged") {
        //    logic.isMultipleTemplates();
        getDevice.call();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.device,
        builder: (logic) {
          logic.style.isOffsetConnect = false;
          bool connected = logic.style.isConnectDevice;
          String machineAlias = logic.machineAlias;
          return logic.parameter?.templateMoudleNew != null
              ? Container(
                  margin: EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: logic.style.titleBgColor,
                    borderRadius: BorderRadius.circular(12.0), // 设置圆角
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(12, 12, 12, 6),
                        child: Row(
                          children:  !connected || machineAlias.isEmpty ? [
                            Text(logic.style.deviceTitle),
                            const Spacer(),
                            GestureDetector(
                              onTap: () {
                                logic.channel.trackEvent("click", "024_066_093");
                                logic.openConnectPage();
                              },
                              child: Row(
                                children: [
                                  SvgIcon(
                                    connected
                                        ? logic.style.isWifi == 1
                                            ? logic.style.wifiIcon
                                            : logic.style.connectBlueToothIcon
                                        : logic.style.blueToothIcon,
                                    side: 16,
                                    matchTextDirection: true,
                                  ),
                                  SizedBox(
                                    width: 2,
                                  ),
                                  Text(
                                    connected
                                        ? logic.style.connectTitle
                                        : logic.getI18nString("app00190", "未连接"),
                                    style: logic.style.deviceTitleStyle,
                                  ),
                                  SvgIcon(
                                    logic.style.blueToothArrow,
                                    side: 16,
                                    matchTextDirection: true,
                                  )
                                ],
                              ),
                            )
                          ] : [
                            Text(logic.style.deviceTitle),
                            const SizedBox(width: 32),
                            Expanded(child: GestureDetector(
                                onTap: () {
                                  logic.channel.trackEvent("click", "024_066_093");
                                  logic.openConnectPage();
                                },
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                    SvgIcon(logic.style.isWifi == 1 ? logic.style.wifiIcon : logic.style.connectBlueToothIcon,
                                      side: 16,
                                      matchTextDirection: true,
                                    ),
                                    Flexible(child: Text(machineAlias, style: const TextStyle(color: Color(0x993C3C43), fontSize: 13.0, fontWeight: FontWeight.w400), maxLines: 1, overflow: TextOverflow.ellipsis))
                                  ]),
                                  Text(logic.style.connectTitle,
                                    style: const TextStyle(color: Color(0x993C3C43), fontSize: 10.0, fontWeight: FontWeight.w400),
                                  )
                                ]
                              ),
                            )),
                            GestureDetector(
                              onTap: () {
                                logic.channel.trackEvent("click", "024_066_093");
                                logic.openConnectPage();
                              },
                              child: SvgIcon(
                                logic.style.blueToothArrow,
                                side: 16,
                                matchTextDirection: true,
                              ),
                            )
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(12, 0, 12, 6),
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                deviceShow = !deviceShow;
                                FocusScope.of(context).unfocus();
                                if (deviceShow) {
                                  logic.channel.trackEvent("click", "024_294_263");
                                  logic.style.deviceShowColor = Color(0x173C3C43);
                                  logic.style.deviceMsgTitle = logic.getI18nString("app100001520", "收起");
                                  logic.style.deviceMsgIcon = "assets/up_arrow.svg";
                                } else {
                                  logic.style.deviceShowColor = Colors.white;
                                  logic.style.deviceMsgTitle = logic.getI18nString("app100001733", "更多信息");
                                  logic.style.deviceMsgIcon = "assets/down_arrow.svg";
                                }

                                logic.update([UpdateFieldManager.device]);
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    constraints: BoxConstraints(maxWidth: 150.0),
                                    child: Text(
                                      logic.style.deviceMsgTitle,
                                      maxLines: 5,
                                      overflow: TextOverflow.ellipsis,
                                      style: logic.style.offsetStyle,
                                    ),
                                  ),
                                  SvgIcon(
                                    logic.style.deviceMsgIcon,
                                    side: 16,
                                    matchTextDirection: true,
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              child: Container(
                                margin: EdgeInsetsDirectional.symmetric(horizontal: 4),
                                width: 0.5,
                                height: 20,
                                color: Color(0x173C3C43), // 线条颜色
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                logic.style.isOffsetConnect = false;
                                logic.channel.trackEvent("click", "024_294_305", eventData: {"source": 1});
                                if (!logic.style.isConnectDevice) {
                                  DialogUtil().showCustomDialog(context, "", logic.getI18nString("app00324", "请先连接打印机"),
                                      contentTextStyle: const TextStyle(
                                          color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
                                      leftFunStr: logic.getI18nString("app00030", "取消"),
                                      rightFunStr: logic.getI18nString("app00034", "连接"), rightFunCall: () {
                                    logic.style.isOffsetConnect = true;
                                    logic.openConnectPage();
                                      },
                                      rightBackgroundColor: logic.style.printSettingThemeColor,
                                      withButtonOutline: true,
                                      channel: logic.channel);

                                  return;
                                }
                                showModalBottomSheet(
                                  context: context,
                                  isScrollControlled: true,
                                  isDismissible: true,
                                  enableDrag: false,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                                  builder: (BuildContext context) {
                                    // 返回 PrintSlowPage 作为底部导航的内容
                                    return PrintOffsetWidget(logic);
                                  },
                                );
                              },
                              child: Container(
                                  padding: EdgeInsetsDirectional.symmetric(vertical: 4, horizontal: 11),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6.0), // 设置圆角
                                      color: Color(0xFFF7F7FA)),
                                  child: Text(
                                    logic.style.offsetTitle,
                                    style: logic.style.offsetStyle,
                                  )),
                            ),
                          ],
                        ),
                      ),
                      deviceShow ? _deviceMsg() : Container()
                    ],
                  ),
                )
              : SizedBox();
        });
  }

  _deviceMsg() {
    List<Widget> widgetList = deviceMsgList
        .sublist(2) // 获取从下标 3 开始的子列表
        .map((msg) {
      return _deviceMsgItem(msg);
    }).toList();
    return Container(
      padding: EdgeInsetsDirectional.fromSTEB(12, 0, 12, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [_deviceMsgItem(deviceMsgList[0]), _deviceMsgItem(deviceMsgList[1])],
              ),
              Expanded(child: Container()),
              GestureDetector(
                onTap: () {
                  logic.channel.trackEvent("click", "024_294_264");
                  IconToast.show(context, logic.getI18nString("pc0170", "复制成功"));
                  String copyData = "";
                  deviceMsgList.forEach((value) {
                    copyData += value + "\n";
                  });

                  final data = ClipboardData(text: copyData);
                  Clipboard.setData(data);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(28), // 设置圆角
                  ),
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 5),
                  child: Text(
                    logic.getI18nString("app00361", "复制"),
                    style: TextStyle(color: Color(0xFF1D1D29), fontSize: 11.0, fontWeight: FontWeight.w400),
                  ),
                ),
              )
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: widgetList,
          )
        ],
      ),
    );
  }

  Widget _deviceMsgItem(String title) {
    return Padding(
      padding: EdgeInsetsDirectional.symmetric(vertical: 1),
      child: Text(
        title,
        style: logic.style.deviceMsgStyle,
      ),
    );
  }
}

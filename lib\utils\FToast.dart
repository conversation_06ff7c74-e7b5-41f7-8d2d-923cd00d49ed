import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';

class IconToast {
  static late IconToast _instance;

  IconToast._internal();

  factory IconToast() {
    return _instance;
  }

  static show(BuildContext context, String title) {
    FToast().init(context).showToast(
        child: showIconToast(title),
        toastDuration: Duration(milliseconds: 1000),
        positionedToastBuilder: (context, child, gravity) {
          return Container(
            child: child,
          );
        });
  }
}

showIconToast(String title) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(10.0),
      color: Colors.black.withOpacity(0.5),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            SvgIcon(
              'assets/success.svg',
              fit: BoxFit.cover,
              side: 20,
            ),
          ],
        ),
        SizedBox(
          width: 12.0,
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: Color(0xFFFFFFFF)),
        ),
      ],
    ),
  );
}

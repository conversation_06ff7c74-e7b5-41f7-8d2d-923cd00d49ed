import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nety/models/index.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_setting_plugin/print/model/print_device_model.dart';
import 'package:niimbot_print_setting_plugin/print/print_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/dialog_util.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:uuid/uuid.dart';
import '../printer_strategy_manager.dart';

class CheckBeforePrint {
  var ignoreCheckColorReverse = false;
  var isCheckUnMatchPaper = false;
  var isCheckPaperForDouble = false;
  var isCheckMachineForDouble = false;
  var isCheckTemplateForSize = false;
  late PrintSettingLogic logic;
  NiimbotPrinter? connectedPrinter;
  PrintDeviceModel? deviceModel;
  String paperColor = "";
  String ribbonColor = "";

  Future<bool> check(PrintSettingLogic printLogic) async {
    logic = printLogic;
    connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    Map printerDetail = await logic.channel
        .getCurrentPrinterInfo((connectedPrinter?.code ?? 0).toString(), connectedPrinter?.name ?? '');
    deviceModel = printerDetail.isEmpty ? null : PrintDeviceModel.fromJson(Map<String, dynamic>.from(printerDetail));
    Map result = printLogic.channel.getPaperRibbonColor();
    paperColor = result['paperColor'] ?? '';
    ribbonColor = result['ribbonColor'] ?? '';

    return checkWithDialog();
  }

  Future<bool> checkWithDialog() async {
    if (!await checkDeviceAvailable()) return false;
    if (!await checkColorReversePrint()) return false;
    if (!await checkUnMatchPager()) return false;
    if (!await checkPaperForDoublePrint()) return false;
    if (!await checkMachineForDoublePrint()) return false;
    if (logic.taskType == PrintTaskType.batch) {
      if (!await checkTemplateForSizePrint()) return false;
    }
    return true;
  }

  /// 检查设备可用性
  Future<bool> checkDeviceAvailable() async {
    Completer<bool> completer = Completer<bool>();
    if (NiimbotPrintSDK().store.connectedPrinter == null) {
      DialogUtil().showCustomDialog(logic.pageManager.context, "", logic.getI18nString("app00324", "请先连接打印机"),
          contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
          leftFunStr: logic.getI18nString("app00030", "取消"),
          rightFunStr: logic.getI18nString("app00034", "连接"), rightFunCall: () {
        logic.openConnectPage();
        completer.complete(false);
      }, leftFunCall: () {
        completer.complete(false);
      }, withButtonOutline: true, rightBackgroundColor: logic.style.printBtnBgColor, channel: logic.channel);
      return completer.future;
    } else {
      return true;
    }
  }

  /// 检查是否支持反白打印
  Future<bool> checkColorReversePrint() async {
    if (ignoreCheckColorReverse) {
      return true;
    }
    if (connectedPrinter == null) {
      return true;
    }
    if (deviceModel?.rfidType != 2 && deviceModel?.rfidType != 3) {
      return true;
    }
    if (!logic.parameter!.templateMoudleNew!.elements.any((element) {
      var map = element.toJson();
      return map['colorReverse'] == 1;
    })) {
      return true;
    }
    Completer<bool> completer = Completer<bool>();
    DialogUtil().showCustomDialog(
        logic.pageManager.context, "", "【C4】" + logic.getI18nString("app100001728", "当前打印机启用反白效果可能造成碳带断裂，请谨慎使用"),
        contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
        leftFunStr: logic.getI18nString("app00030", "取消"),
        rightFunStr: logic.getI18nString("app100000054", "忽略，仍要打印"), rightFunCall: () {
      ignoreCheckColorReverse = true;
      completer.complete(true);
    }, leftFunCall: () {
      completer.complete(false);
    }, channel: logic.channel);
    return completer.future;
  }

  /// 检查耗材不匹配
  Future<bool> checkUnMatchPager() async {
    if (isCheckUnMatchPaper) {
      return true;
    }
    //判断是否时相片纸不支持当前标签机
    bool isPhotoFramePaperNotSupport = await PrinterStrategyManager().isPhotoFramePaperNotSupportDeveice(logic);
    if (PrintManager().rfidTemplate == null ||
        (!PrinterStrategyManager().isSupportRFIDPrint && !isPhotoFramePaperNotSupport)) {
      return true;
    }
    if (PrintManager().rfidTemplate!.profile.machineName!.split(",").contains(deviceModel?.name ?? "")) {
      return true;
    }
    List<NiimbotRFIDInfo> rfidInfos = await NiimbotPrintSDK().getConsumablesRFIDData();
    final densityType =
        await PrinterStrategyManager().printCheckRFIDPrintStrategy(logic, Uuid().v4(), (languageCode, descrp) {
      return logic.getI18nString(languageCode, descrp);
    }, rfidInfos: rfidInfos,isNeedTrackEvent: false);
    if (densityType == StrategyType.forbid) {
      //打印策略如果为禁止打印时直接弹窗返回 不再执行后续流程
      return false;
    }
    Completer<bool> completer = Completer<bool>();
    DialogUtil().showCustomDialog(
        logic.pageManager.context, "", "【C1】" + logic.getI18nString("app100000901", "当前耗材与打印机不匹配，继续打印会影响打印效果"),
        contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
        leftFunStr: logic.getI18nString("app00030", "取消"),
        rightFunStr: logic.getI18nString("app100000054", "忽略，仍要打印"), rightFunCall: () {
      isCheckUnMatchPaper = true;
      completer.complete(true);
    }, leftFunCall: () {
      completer.complete(false);
    }, channel: logic.channel);
    return completer.future;
  }

  /// 检查纸张是否支持双色打印
  Future<bool> checkPaperForDoublePrint() async {
    if (isCheckPaperForDouble) {
      return true;
    }
    bool isSingleColorCarbon = paperColor.split(',').length == 1 || ribbonColor.split(',').length == 1;
    if (hasPaperColorElement(logic.parameter!.templateMoudleNew!.elements) &&
        paperColor.split(',').length < 2 &&
        ribbonColor.split(',').length < 2 &&
        !isSingleColorCarbon) {
      Completer<bool> completer = Completer<bool>();
      DialogUtil().showCustomDialog(
          logic.pageManager.context, "", "【C2】" + logic.getI18nString("app100000417", "当前耗材不支持红黑双色打印，继续打印会影响打印效果"),
          contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
          leftFunStr: logic.getI18nString("app00030", "取消"),
          rightFunStr: logic.getI18nString("app100000054", "忽略，仍要打印"), rightFunCall: () {
        isCheckPaperForDouble = true;
        completer.complete(true);
      }, leftFunCall: () {
        completer.complete(false);
      }, channel: logic.channel);
      return completer.future;
    } else {
      return true;
    }
  }

  /// 检查设备是否支持双色打印
  Future<bool> checkMachineForDoublePrint() async {
    if (isCheckMachineForDouble) {
      return true;
    }
    var colorsStr = logic.style.carbonColor.split(",");
    bool isMultiColorCarbon = logic.style.isCarbon && colorsStr.length > 1;
    bool isSingleColorCarbon = paperColor.split(',').length == 1 || ribbonColor.split(',').length == 1;
    if (hasPaperColorElement(logic.parameter!.templateMoudleNew!.elements) &&
        !isDeviceSupportDoubleColorPrint() &&
        !isMultiColorCarbon &&
        !isSingleColorCarbon) {
      Completer<bool> completer = Completer<bool>();
      DialogUtil().showCustomDialog(
          logic.pageManager.context, "", "【C3】" + logic.getI18nString("app100000418", "当前机器不支持红黑双色打印，继续打印会影响打印效果"),
          contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
          leftFunStr: logic.getI18nString("app00030", "取消"),
          rightFunStr: logic.getI18nString("app100000054", "忽略，仍要打印"), rightFunCall: () {
        isCheckMachineForDouble = true;
        completer.complete(true);
      }, leftFunCall: () {
        completer.complete(false);
      }, channel: logic.channel);
      return completer.future;
    } else {
      return true;
    }
  }

  /// 检查模板尺寸是否支持打印
  Future<bool> checkTemplateForSizePrint() async {
    if (isCheckTemplateForSize) {
      return true;
    }
    if (PrintManager().rfidTemplate == null) {
      return true;
    }
    bool isSame = true;
    if (logic.parameter?.batchtIds != null) {
      for (var value in logic.parameter!.batchtIds!) {
        Map templateData = await logic.channel.getBatchPrintTemplate(value["id"]);
        if (templateData["width"] != PrintManager().rfidTemplate?.width ||
            templateData["height"] != PrintManager().rfidTemplate?.height) {
          isSame = false;
          break; // 符合条件时跳出循环
        }
      }
    }

    if (!isSame) {
      String size =
          "${getRealitySize(PrintManager().rfidTemplate!.width.toString())}x${getRealitySize(PrintManager().rfidTemplate!.height.toString())}mm";

      Completer<bool> completer = Completer<bool>();
      DialogUtil().showCustomDialog(logic.pageManager.context, "",
          "【C5】" + logic.getI18nString("app100001277", "模板与已安装标签纸尺寸（\$）不一致，请重新选择模板或更换标签纸", param: [size]),
          contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
          leftFunStr: logic.getI18nString("app00030", "取消"),
          rightFunStr: logic.getI18nString("app100000054", "忽略，仍要打印"), rightFunCall: () {
        isCheckTemplateForSize = true;
        completer.complete(true);
      }, leftFunCall: () {
        completer.complete(false);
      }, channel: logic.channel);
      return completer.future;
    } else {
      return true;
    }
  }

  String getRealitySize(String size) {
    if (size.contains('.')) {
      String after = size.split('.').last;
      if (int.parse(after) == 0) {
        return size.split('.').first;
      } else {
        String trimEnd = after.replaceAll(RegExp(r'0+$'), '');
        return '${size.split('.').first}.$trimEnd';
      }
    } else {
      return size;
    }
  }

  bool hasPaperColorElement(List<BaseElement> elements) {
    // 定义参考颜色
    // final referenceColor = Color.fromARGB(255, 0, 0, 0);

    // 检查列表中的每一个元素
    return elements.any((element) {
      if (element is TableElement) {
        return (element.contentColorChannel != 0) || (element.lineColorChannel != 0);
      } else {
        var map = element.toJson();
        return (map['paperColorIndex'] ?? 0) != 0;
      }
    });
  }

  bool areColorsEqual(Color color1, Color color2) {
    return color1.alpha == color2.alpha &&
        color1.red == color2.red &&
        color1.green == color2.green &&
        color1.blue == color2.blue;
  }

  List<int> getPrintColor() {
    List<int> result = [];

    if (deviceModel?.rfidType == 1) {
      if (isSupportGray16Print()) {
        return [255, 0, 0, 0];
      }

      if (paperColor != null && paperColor.isNotEmpty && !paperColor.contains(',')) {
        List<String> temp = paperColor.split('.').toList();
        temp.insert(0, paperColor == "255.255.255" ? "0" : "255");
        result = temp.map((it) {
          try {
            return int.parse(it);
          } catch (e) {
            print(e);
            return 0;
          }
        }).toList();
      }
    } else {
      if (ribbonColor != null && ribbonColor.isNotEmpty) {
        result = [255, 0, 0, 0];
      }
    }

    return result;
  }

  ///是否支持16色灰阶打印
  bool isSupportGray16Print() {
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrinter == null) return false;
    return PrintManager().printColors == '159.160.160' &&
        (connectedPrinter.colorModeSupport?.contains(NiimbotPrintColorMode.gray16) ?? false);
  }

  bool isDeviceSupportDoubleColorPrint() {
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrinter == null) return false;
    return (connectedPrinter.colorModeSupport?.contains(NiimbotPrintColorMode.multi) ?? false);
  }
}

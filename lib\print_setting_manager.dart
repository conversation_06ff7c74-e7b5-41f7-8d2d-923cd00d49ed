import 'package:flutter/material.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/interface/print_setting_channel_interface.dart';
import 'package:niimbot_print_setting_plugin/model/print_paramter.dart';
import 'package:niimbot_print_setting_plugin/page/print_contain_page.dart';
import 'package:niimbot_print_setting_plugin/page/print_high_page.dart';
import 'package:niimbot_print_setting_plugin/page/print_middle_page.dart';
import 'package:niimbot_print_setting_plugin/page/print_slow_page.dart';
import 'package:niimbot_print_setting_plugin/print_page_manager.dart';
///份数设置场景
enum PrintCopiesMode {
  //设置份数
  settingCopies,
  //关联份数
  linkCopies,
}

enum PrintTaskType {
  canvas,
  detail,
  batch,
  normal,
  printJustOne,
  miniApp,
  folderShare,
  printNullUi,
  printSceneHistory,
  printC1,
  /// 打印背景无UI但是显示进度
  printNullUiShowProgress,
}

extension PrintTaskTypeExtension on PrintTaskType {
  String get name {
    switch (this) {
      case PrintTaskType.canvas:
        return 'print_scene_canvas';
      case PrintTaskType.detail:
        return 'print_scene_template_detail';
      case PrintTaskType.batch:
        return 'print_batch';
      case PrintTaskType.printJustOne:
        return 'print_just_one';
      case PrintTaskType.miniApp:
        return 'mini_app';
      case PrintTaskType.folderShare:
        return 'folder_share';
      case PrintTaskType.printNullUi:
        return 'print_null_ui';
      case PrintTaskType.printSceneHistory:
        return 'print_scene_history';
      case PrintTaskType.printC1:
        return 'print_C1_ui';
      case PrintTaskType.printNullUiShowProgress:
        return 'print_null_ui_show_progress';
      default:
        return 'Unknown Task';
    }
  }

  static PrintTaskType fromScene(String scene) {
    switch (scene) {
      case 'print_scene_template_detail':
        return PrintTaskType.detail;
      case 'print_batch':
        return PrintTaskType.batch;
      case 'print_scene_canvas':
        return PrintTaskType.canvas;
      case 'print_just_one':
        return PrintTaskType.printJustOne;
      case 'mini_app':
        return PrintTaskType.miniApp;
      case "folder_share":
        return PrintTaskType.folderShare;
      case "print_null_ui":
        return PrintTaskType.printNullUi;
      case "print_C1_ui":
        return PrintTaskType.printC1;
      case "print_scene_history":
        return PrintTaskType.printSceneHistory;
      case "print_null_ui_show_progress":
        return PrintTaskType.printNullUiShowProgress;
      default:
        return PrintTaskType.normal;
    }
  }
}

enum PrintPageType {
  none,
  low,
  middle,
  high,
}

class PrintSettingManager {
  // 定义打印任务类型，初始设置为普通打印
  PrintTaskType taskType = PrintTaskType.normal;

  // 定义打印页面类型，初始设置为高页面打印
  PrintPageType pageType = PrintPageType.high;

  // 打印参数对象，可能为空
  PrintParameter? parameter;

  // 创建打印页面管理器实例
  late PrintPageManager pageManager;

  // 导航状态，初始设置为true表示启用导航
  bool isNavigator = true;
  final String Function(String stringCode, String defaultStr, {List<String>? param})? getI18nString;

  /**
   * 打印设置管理器构造函数
   * 负责根据传入的参数初始化打印参数对象
   *
   * @param parameter 包含打印相关设置的字典，用于初始化打印参数对象
   */
  PrintSettingManager(BuildContext context, Map<String, dynamic> parameter, this.getI18nString,
      {Widget Function(BuildContext context, PrintSettingLogic logic)? titleWidget,
      Widget Function(BuildContext context, PrintSettingLogic logic)? previewWidget,
      Widget Function(BuildContext context, PrintSettingLogic logic)? deviceWidget,
      Widget Function(BuildContext context, PrintSettingLogic logic)? settingWidget,
      Widget Function(BuildContext context, PrintSettingLogic logic)? bottomWidget}) {
    this.parameter = PrintParameter.fromJson(parameter);
    if (this.parameter?.taskType != null) {
      this.taskType = this.parameter!.taskType!;
    }
    this.pageManager = PrintPageManager(context, this.getI18nString);

    if (titleWidget != null) {
      pageManager.setTitleWidget(titleWidget);
    }
    if (previewWidget != null) {
      pageManager.setPreviewWidget(previewWidget);
    }
    if (deviceWidget != null) {
      pageManager.setDeviceWidget(deviceWidget);
    }
    if (settingWidget != null) {
      pageManager.setSettingWidget(settingWidget);
    }
    if (bottomWidget != null) {
      pageManager.setBottomBtnWidget(bottomWidget);
    }
  }

  /// 设置打印任务类型
  /// [type] 打印任务类型
  setTaskType(PrintTaskType type) {
    taskType = type;
    return this;
  }

  /// 设置打印页面类型
  /// [type] 打印页面类型
  setPageType(PrintPageType type) {
    pageType = type;
    return this;
  }

  /// 设置是否为导航模式
  /// [isNavigator] 是否为导航模式的布尔值
  setIsNavigator(bool isNavigator) {
    this.isNavigator = isNavigator;
    return this;
  }

  /// 展示打印页面
  /// [context] BuildContext对象，用于访问Dart框架的组件构建环境
  show(BuildContext context, PrintSettingChannelInterface channel) {
    return _initPageStyle(context, channel);
  }

  /// 初始化页面样式
  /// [context] BuildContext对象，用于访问Dart框架的组件构建环境
  /// 返回页面管理器的实例
  _initPageStyle(BuildContext context, PrintSettingChannelInterface channel) {
    pageManager.build(taskType);
    return _toPage(context, channel);
  }

  /// 根据不同的页面类型跳转到相应的页面
  ///
  /// [context] 用于页面构建的上下文
  ///
  /// 根据[pageType]变量的值，跳转到低、中、高三种不同级别的页面
  _toPage(BuildContext context, PrintSettingChannelInterface channel) {
    switch (pageType) {
      case PrintPageType.none:
        return _buildNonePage(context, taskType, parameter, channel);
      case PrintPageType.low:
        return _buildLowPage(context, taskType, parameter, channel);
      case PrintPageType.middle:
        return _buildMiddlePage(context, taskType, parameter, channel);
      case PrintPageType.high:
        return _buildHighPage(context, taskType, parameter, channel);
    }
  }

  /// 构建低模态打印页面
  ///
  /// [context] 是构建上下文。
  /// [taskType] 是打印任务类型。
  /// [parameter] 是打印参数，可能为空。
  ///
  /// 当 [isNavigator] 为 true 时，使用 showModalBottomSheet 显示一个模态底部导航，
  /// 否则返回一个包含 PrintSlowPage 的 PrintContainPage。
  ///
  /// 不返回任何值。
  _buildNonePage(
      BuildContext context, PrintTaskType taskType, PrintParameter? parameter, PrintSettingChannelInterface channel) {
    if (isNavigator) {
      // 使用 showModalBottomSheet 显示一个模态底部导航
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: false,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        builder: (BuildContext context) {
          // 返回 PrintSlowPage 作为底部导航的内容
          return PrintSlowPage(taskType, parameter, pageManager, channel);
        },
      ).then((value) {});
    } else {
      // 返回一个包含 PrintSlowPage 的 PrintContainPage
      return PrintSlowPage(taskType, parameter, pageManager, channel);
    }
  }

  /// 构建低模态打印页面
  ///
  /// [context] 是构建上下文。
  /// [taskType] 是打印任务类型。
  /// [parameter] 是打印参数，可能为空。
  ///
  /// 当 [isNavigator] 为 true 时，使用 showModalBottomSheet 显示一个模态底部导航，
  /// 否则返回一个包含 PrintSlowPage 的 PrintContainPage。
  ///
  /// 不返回任何值。
  _buildLowPage(
      BuildContext context, PrintTaskType taskType, PrintParameter? parameter, PrintSettingChannelInterface channel) {
    if (isNavigator) {
      // 使用 showModalBottomSheet 显示一个模态底部导航
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: false,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        builder: (BuildContext context) {
          // 返回 PrintSlowPage 作为底部导航的内容
          return PrintSlowPage(taskType, parameter, pageManager, channel);
        },
      ).then((value) {});
    } else {
      // 返回一个包含 PrintSlowPage 的 PrintContainPage
      return PrintContainPage(child: PrintSlowPage(taskType, parameter, pageManager, channel));
    }
  }

  _buildMiddlePage(
      BuildContext context, PrintTaskType taskType, PrintParameter? parameter, PrintSettingChannelInterface channel) {
    if (isNavigator) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: false,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        builder: (BuildContext context) {
          return PrintMiddlePage(taskType, parameter, pageManager, channel);
        },
      ).then((value) {});
    } else {
      return PrintContainPage(child: PrintMiddlePage(taskType, parameter, pageManager, channel));
    }
  }

  _buildHighPage(
      BuildContext context, PrintTaskType taskType, PrintParameter? parameter, PrintSettingChannelInterface channel) {
    if (isNavigator) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => PrintHighPage(taskType, parameter, pageManager, channel)),
      );
    } else {
      return PrintHighPage(taskType, parameter, pageManager, channel);
    }
  }
}

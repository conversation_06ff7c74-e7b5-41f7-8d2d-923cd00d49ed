import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/pdf_bind_info.dart';
import 'package:niimbot_print_setting_plugin/utils/DebounceUtil.dart';
import 'package:niimbot_print_setting_plugin/utils/file_back_image.dart';
import 'package:niimbot_print_setting_plugin/utils/nb_image_view.dart';
import 'package:niimbot_print_setting_plugin/utils/print_preview_image_util.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';
import 'package:niimbot_print_setting_plugin/widget/attr_icon_button.dart';
import 'package:niimbot_template/niimbot_template.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrintSettingPreview extends StatefulWidget {
  final List<TemplateData> templateDatas;
  final ValueChanged<int>? onPageIndexSelect;
  final BuildContext parentContext;
  final List<Color?>? printColors;
  final int crossAxisCount;
  final bool isBatchPrint;
  List<Map<String, dynamic>>? batchtIds;
  final int selectIndex;
  final PrintSettingLogic logic;
  PrintSettingPreview(
    this.logic,
    this.templateDatas,
    this.parentContext,
    this.printColors,
    this.crossAxisCount,
    this.isBatchPrint,
    this.batchtIds,
    this.selectIndex,
    this.onPageIndexSelect, {
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return PrintSettingPreviewState(logic);
  }
}

class PrintSettingPreviewState extends State<PrintSettingPreview> {
  bool _loadList = false;
  bool _loadItemImage = false;
  int _crossAxisCount = 2;
  bool _isSupport16Gray = false;
  RefreshController refreshController = RefreshController();
  ScrollController scrollController = ScrollController();
  final PrintSettingLogic logic;

  PrintSettingPreviewState(this.logic);
  @override
  void initState() {
    super.initState();
    clearBatchPreviewCache();
    clearBatchTemplateCache();
    _crossAxisCount = widget.crossAxisCount;
    if(widget.templateDatas.isNotEmpty) {
      _isSupport16Gray = logic.checkCurrentIsSupportGray16(widget.templateDatas.first);
    }
    int pages = 0;
    if (widget.isBatchPrint) {
      pages = widget.batchtIds!.length;
    } else {
      pages = logic.style.pageMax;
    }
    for (int i = 0; i < pages; i++) {
      dataList.add(i);
    }
    if (logic.isShowRFID) {
      RfidRepository.rfidInfoManager.templateData = widget.templateDatas.first;
    }
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        _loadList = true;
      });
    });
  }

  int pageCount = 0;
  int startIndex = 0;
  int endIndex = 0;
  double itemHeight = 0;
  double itemWidth = 0;
  double imageWidth = 0;
  double imageHeight = 0;
  double childAspectRatio = 0;
  List<int> dataList = [];
  List<int> currentList = [];

  _calcItem() {
    if (dataList.isEmpty) return;
    if (pageCount == 0) {
      /// 子元素宽高比
      childAspectRatio = (widget.templateDatas.first.width / widget.templateDatas.first.height);
      itemWidth =
          ((MediaQuery.sizeOf(context).width - 20 * 2) /** GridView 宽度 */ - (_crossAxisCount - 1) * 16) /** 列间隙宽度 */ /
              _crossAxisCount /** 列数 */;

      itemHeight = itemWidth / childAspectRatio + 23 /** 字体高度 */ + 12 /** padding*/;

      if (itemHeight > 220) {
        itemHeight = 220.toDouble();
        imageHeight = (220 - 23 - 12).toDouble();
        imageWidth = imageHeight * childAspectRatio;
      } else {
        imageHeight = (itemWidth - 24) / childAspectRatio;
        imageWidth = itemWidth - 24;
      }

      childAspectRatio = itemWidth / itemHeight;
      if (widget.isBatchPrint && _crossAxisCount == 2) {
        itemHeight = 100 + 4 + 12 + 9;
      }
      pageCount = (_crossAxisCount * ((MediaQuery.sizeOf(context).height - 88) ~/ itemHeight)) +
          _crossAxisCount + _crossAxisCount;
      // 增加单行时列表一次加载数量
      if (_crossAxisCount == 1) pageCount = 10;

      int maxNumber = widget.isBatchPrint ? widget.batchtIds!.length : logic.style.pageMax;
      startIndex = widget.selectIndex ~/ pageCount * pageCount;
      endIndex = (widget.selectIndex ~/ pageCount + 1) * pageCount > maxNumber
          ? maxNumber
          : (widget.selectIndex ~/ pageCount + 1) * pageCount;
    }
    currentList = dataList.sublist(startIndex, endIndex);
  }

  bool _isContainSerialNumberOnly() {
    bool isSerialNumberOnly = true;
    if (!widget.isBatchPrint) {
      isSerialNumberOnly = false;
    }
    return isSerialNumberOnly;
  }

  _onRefresh() {
    Future.delayed(const Duration(milliseconds: 1000), () {
      setState(() {
        startIndex -= pageCount;
        if (startIndex < 0) startIndex = 0;
        refreshController.refreshCompleted();
      });
    });
  }

  _onLoadMore() {
    Future.delayed(const Duration(milliseconds: 1000), () {
      setState(() {
        endIndex += pageCount;
        if (widget.isBatchPrint) {
          if (endIndex >= widget.batchtIds!.length) {
            endIndex = widget.batchtIds!.length;
            refreshController.loadNoData();
          } else {
            refreshController.loadComplete();
          }
        } else {
          if (endIndex >= logic.style.pageMax) {
            endIndex = logic.style.pageMax;
            refreshController.loadNoData();
          } else {
            refreshController.loadComplete();
          }
        }
      });
    });
  }

  _switchCrossCount() {
    if (!DebounceUtil.checkClick()) return;
    clearBatchPreviewCache();
    clearBatchTemplateCache();
    setState(() {
      if (_crossAxisCount == 2) {
        _crossAxisCount = 1;
      } else if (_crossAxisCount == 1) {
        _crossAxisCount = 2;
      }
      SharedPreferences.getInstance().then((sp) {
        sp.setInt("batch_cross_axis_count", _crossAxisCount);
      });
      pageCount = 0;
      currentList.clear();
      refreshController.loadComplete();
    });
  }

  @override
  Widget build(BuildContext context) {
    _calcItem();
    return Container(
      decoration: const BoxDecoration(
          color: Color(0xFFF5F5F5),
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
              boxShadow: [
                BoxShadow(
                  blurRadius: 5, //阴影范围
                  spreadRadius: 0.1, //阴影浓度
                  offset: const Offset(0, 0.0),
                  color: Colors.grey.withOpacity(0.15), //阴影颜色
                )
              ],
            ),
            height: 48,
            child: Row(
              children: [
                AttrIconButton(
                  SvgPicture.asset('assets/common/sheet_close.svg',
                      package: 'niimbot_flutter_canvas', width: 24, height: 24, fit: BoxFit.none),
                  width: 48,
                  height: 48,
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                ),
                const Spacer(),
                AttrIconButton(
                  SizedBox(
                    height: 24,
                    width: 24,
                    child: SvgPicture.asset(
                      _crossAxisCount == 2 ? 'assets/common/sheet_grid.svg' : 'assets/common/sheet_column.svg',
                      package: 'niimbot_flutter_canvas',
                    ),
                  ),
                  width: 48,
                  height: 48,
                  onTap: _switchCrossCount,
                ),
              ],
            ),
          ),
          Expanded(
              child: _loadList
                  ? Container(
                      padding: EdgeInsetsDirectional.fromSTEB(20, 10, 20, 10),
                      child: MediaQuery.removePadding(
                        context: context,
                        child: SmartRefresher(
                            controller: refreshController,
                            enablePullDown: true,
                            enablePullUp: true,
                            onRefresh: _onRefresh,
                            onLoading: _onLoadMore,
                            child: MasonryGridView.builder(
                                itemCount: currentList.length,
                                controller: scrollController,
                                physics: const ClampingScrollPhysics(),
                                // 纵轴间距
                                mainAxisSpacing: 9.0,

                                // 横轴间距
                                crossAxisSpacing: 16.0,

                                // SliverGridDelegateWithFixedCrossAxisCount 构建一个横轴固定数量Widget
                                gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
                                  // 横轴元素个数
                                  crossAxisCount: _crossAxisCount,
                                ),
                                // gridDelegate: SliverGridDelegateWithFixedSize(itemWidth, 100, mainAxisSpacing: 10),
                                itemBuilder: (BuildContext context, int index) {
                                  return widget.isBatchPrint
                                      ? buildBatchItem(_loadItemImage, currentList[index])
                                      : buildItem(_loadItemImage, currentList[index]);
                                })),
                      ),
                    )
                  : Container())
        ],
      ),
    );
  }

  buildItem(bool loadImage, int index) {
    return InkWell(
        onTap: () {
          Navigator.of(context).pop();
          widget.onPageIndexSelect?.call(index);
        },
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(
                      color: (widget.selectIndex == index && !widget.isBatchPrint)
                          ? const Color(0xFFFB4B42)
                          : Colors.transparent,
                      width: 1),
                  borderRadius: const BorderRadius.all(Radius.circular(10))),
              child: ClipRRect(
                  // borderRadius: BorderRadius.circular(10),
                  child: Stack(
                alignment: Alignment.center,
                children: [
                  // _buildSelectedBackgroundWidget(index),
                  buildItemImage(loadImage, index),
                ],
              )),
            ),
            const SizedBox(
              height: 4,
            ),
            _buildRFIDWidget(context, index),
            Text(
              '${index + 1}',
              style: const TextStyle(fontSize: 14.0, color: Color(0xFF262626), fontWeight: FontWeight.w400).copyWith(
                  color: (widget.selectIndex == index && !widget.isBatchPrint)
                      ? const Color(0xFFFB4B42)
                      : const Color(0xFF262626),
                  fontWeight: FontWeight.w600),
            )
          ],
        ));
  }

  buildBatchItem(bool loadImage, int index) {
    String id = logic.parameter!.batchtIds![index]["id"];
    int printPage = logic.parameter!.batchtIds![index]["printPage"];
    return InkWell(
        onTap: () {
          Navigator.of(context).pop();
          widget.onPageIndexSelect?.call(index);
        },
        child: Column(
          children: [
            Container(
                height: itemHeight,
                // width: MediaQuery.of(context).size.width / 2 - 30,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                    color: Color(0xFFffefefef),
                    border: Border.all(
                        color: (widget.selectIndex == index) ? const Color(0xFFFB4B42) : Colors.transparent,
                        width: 1),
                    borderRadius: const BorderRadius.all(Radius.circular(10))),
                child: Container(
                  child: buildBatchItemImage(id),
                )),
            const SizedBox(
              height: 4,
            ),
            _buildRFIDWidget(context, index),
            Text(
              '${index + 1}.P ${logic.getI18nString("app100001255", "", param: [
                (printPage * logic.printData.printCount).toString()
              ])}',
              style: const TextStyle(fontSize: 12.0, color: Color(0xFF262626), fontWeight: FontWeight.w400)
                  .copyWith(
                  color: (widget.selectIndex == index) ? const Color(0xFFFB4B42) : const Color(0xFF262626),
                  fontWeight: FontWeight.w600),
            )
          ],
        ));
  }


  buildBatchItemImage(String templateId) {
    return BatchItemDelegate(templateId, widget.logic);
  }

  buildItemImage(bool loadImage, int index) {
    late TemplateData templateData;
    if (widget.isBatchPrint) {
      templateData = widget.templateDatas[index];
      index = 1;
    } else {
      templateData = widget.templateDatas.first;
    }
    return ItemDelegate(imageWidth, imageHeight, templateData, widget.printColors, _isSupport16Gray, _crossAxisCount, index,
        logic.parameter!.pdfBindInfo, loadImage);
  }


  List<Widget> getRfidContentWidget({RFIDShowType? type, required String rfidValue}) {
    List<Widget> rfidContent = [const SizedBox.shrink()];
    switch (type) {
      case RFIDShowType.NotMatch:
        rfidContent = [
          SvgIcon(
            logic.style.rfidIcon,
            color: Color(0xFF595959),
            width: 18,
            height: 12,
            fit: BoxFit.contain,
          ),
          const SizedBox(
            width: 3,
          ),
          SvgIcon(
            logic.style.rfidWarningIcon,
            width: 16,
            height: 16,
          ),
          const SizedBox(
            width: 3,
          ),
          Flexible(
            child: Text(
              logic.pageManager.style.rfidSoureUnCompliance,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFFF8473E)),
            ),
          ),
        ];
        break;
      case RFIDShowType.Normal:
        rfidContent = [
          Padding(
            // 此处的Paddding是为了处理文本和SvgIcon的baseline对齐的问题
            padding: EdgeInsetsDirectional.only(top: 2),
            child: SvgIcon(
              logic.style.rfidIcon,
              color: Color(0xFF595959),
              width: 18,
              height: 12,
              fit: BoxFit.contain,
            ),
          ),
          const SizedBox(
            width: 3,
          ),
          Flexible(
            child: Text(
              rfidValue,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ];
        break;
      default:
        break;
    }
    return rfidContent;
  }

  Widget _buildRFIDWidget(BuildContext context, int index) {
    // rfidContent生成

    RfidShowItem rfidShowItem = RfidRepository().getRfidShowItem(index + 1);
    return logic.isShowRFID == true && rfidShowItem.rfidShowType != RFIDShowType.NotBinding
        ? Padding(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 10.0),
            child: Row(
              mainAxisAlignment: _crossAxisCount == 1 ? MainAxisAlignment.center : MainAxisAlignment.start,
              crossAxisAlignment: rfidShowItem.rfidShowType == RFIDShowType.Normal
                  ? CrossAxisAlignment.start
                  : CrossAxisAlignment.center,
              children: [
                ...getRfidContentWidget(type: rfidShowItem.rfidShowType, rfidValue: rfidShowItem.rfidContent),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  @override
  void dispose() {
    super.dispose();
    clearBatchPreviewCache();
    clearBatchTemplateCache();
  }
}

class ItemDelegate extends StatefulWidget {
  final double width;
  final double height;
  final TemplateData templateData;
  final List<Color?>? printColors;
  final bool support16Gray;
  final int crossAxisCount;
  final int index;
  final bool loadImage;
  final PdfBindInfo? pdfBindInfo;
  const ItemDelegate(
    this.width,
    this.height,
    this.templateData,
    this.printColors,
    this.support16Gray,
    this.crossAxisCount,
    this.index,
    this.pdfBindInfo,
    this.loadImage, {
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _ItemDelegateState();
}

class _ItemDelegateState extends State<ItemDelegate> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<String> backgroudList = widget.templateData.backgroundImage.split(",");
    String selectedLocalPath = widget.templateData.localBackground.length > widget.templateData.multipleBackIndex &&
            widget.templateData.multipleBackIndex >= 0
        ? widget.templateData.localBackground[widget.templateData.multipleBackIndex]
        : "";
    String imageUrl = "";
    if (backgroudList.isNotEmpty) {
      int index = widget.templateData.multipleBackIndex;
      if (index < 0 || index >= backgroudList.length) {
        index = 0;
      }
      imageUrl = backgroudList[index];
    }

    Color? firstRfidColor = (widget.printColors == null || widget.printColors!.isEmpty || widget.support16Gray) ? null : widget.printColors![0]!;
    Color? secondRfidColor = (widget.printColors == null || widget.printColors!.length < 2) ? null : widget.printColors![1]!;
    Future<Uint8List?> imageData = generateBatchTemplatePreview(
        widget.templateData, widget.pdfBindInfo, widget.index + 1, null,
        firstRfidColor: firstRfidColor, secondRfidColor: secondRfidColor, isSupportGray16: widget.support16Gray);
    return SizedBox(
        width: widget.width,
        height: widget.height, //itemHeight - 23 - 12,
        child: NBImageView(imageData, widget.templateData.canvasRotate.toInt(),isBatchPreview:true));
  }
}

class BatchItemDelegate extends StatefulWidget {

  final String templateId;
  final PrintSettingLogic logic;
  const BatchItemDelegate(
      this.templateId,
      this.logic, {
        super.key,
      });

  @override
  State<StatefulWidget> createState() => _BatchItemDelegateState();
}

class _BatchItemDelegateState extends State<BatchItemDelegate> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: getBatchPrintTemplate(widget.logic, widget.templateId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done && (snapshot.data?.isNotEmpty ?? false)) {
          String thumbnail = "";
          String localThumbnail = "";
          Map value = snapshot.data as Map;
          thumbnail = value["thumbnail"];
          localThumbnail = value["local_thumb"] ?? "";
          return FallbackImage(
            localImagePath: localThumbnail, // 本地图片路径
            networkImageUrl: thumbnail, // 网络图片URL
            fit: BoxFit.contain,
          );
        } else {
          return Container(
            height: 125,
            decoration:
            const BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.all(Radius.circular(10))),
          );
        }
      },
    );
  }
}

/// 批量预览缓存
Map<int, Uint8List> _batchPreviewCache = {};
Map<String, Map<String, dynamic>> _batchTemplateCache = {};

/// 清理批量预览缓存
clearBatchPreviewCache() {
  _batchPreviewCache.clear();
}

clearBatchTemplateCache() {
  _batchTemplateCache.clear();
}

/// 是否已缓存图像
bool hasCacheImage(int pageIndex) {
  return _batchPreviewCache.containsKey(pageIndex);
}

bool hasCacheTemplate(String templateId) {
  return _batchTemplateCache.containsKey(templateId);
}

/// 批量模板生成预览图
Future<Uint8List?> generateBatchTemplatePreview(
    TemplateData templateData, PdfBindInfo? pdfBindInfo, int pageIndex, Color? printColor,
    {double preRatio = 8,
    String backgroundImage = '',
    String localBackgroundImageUrl = '',
    Color? firstRfidColor,
    Color? secondRfidColor,
      bool isSupportGray16 = false }) async {
  if (_batchPreviewCache.containsKey(pageIndex)) {
    /// 命中缓存
    return _batchPreviewCache[pageIndex];
  }
  TemplateData cuttentTemplateData = templateData.copyWith();
  if (pdfBindInfo != null) {
    List<BaseElement> baseElements = [];
    for (var element in cuttentTemplateData.elements) {
      if (element is ImageElement && pdfBindInfo.elementIsBindPdf(element.id)) {
        List<String> imagePaths = pdfBindInfo.getPDFImagePathsWithElementId(element.id);
        String localImagePath = pageIndex - 1 >= imagePaths.length ? "" : imagePaths[pageIndex - 1];
        ImageElement imageElement = element.copyWith(localImageUrl: localImagePath);
        baseElements.add(imageElement);
      } else {
        baseElements.add(element);
      }
    }
    cuttentTemplateData = templateData.copyWith(elements: baseElements);
  }

  /// 调用图像库生成预览
  Uint8List imageData = await PrintPreviewImageUtil.generatePreviewImage(cuttentTemplateData,
      page: pageIndex, firstRfidColor: firstRfidColor, secondRfidColor: secondRfidColor, isSupportGray16: isSupportGray16,correctRatio: 10);
  _batchPreviewCache[pageIndex] = imageData;
  return _batchPreviewCache[pageIndex];
}

Future<Map<String, dynamic>?> getBatchPrintTemplate(PrintSettingLogic logic, String templateId) async {
  if (hasCacheTemplate(templateId)) {
    return _batchTemplateCache[templateId];
  }
  var templateData = await logic.channel.getBatchPrintTemplate(templateId);
  _batchTemplateCache[templateId] = templateData;
  return _batchTemplateCache[templateId];
}

import 'dart:io';

import 'package:flutter/material.dart';

class FallbackImage extends StatelessWidget {
  final String localImagePath;
  final String networkImageUrl;
  final BoxFit fit;

  FallbackImage({required this.localImagePath, required this.networkImageUrl, this.fit = BoxFit.cover});

  @override
  Widget build(BuildContext context) {
    File localImage = File(localImagePath);
    return Image.file(localImage, fit: fit, errorBuilder: (_, __, ___) => Image.network(networkImageUrl));
  }
}

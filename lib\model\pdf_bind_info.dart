import 'dart:io';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/template_data.dart';

class PdfBindInfo {
  //pdf与模版元素绑定信息
  List<Map<String, dynamic>> pdfBindElementInfos = [];

  PdfBindInfo(this.pdfBindElementInfos);

  ///获取导入PDF可打印数
  int getPDFEnablePrintMaxNumber(TemplateData? templateData, {Map<String, dynamic> templateDaMapInfo = const {}}) {
    int enablePrintMax = 0;
    if (templateData != null) {
      for (var element in templateData.elements) {
        if (element.type == NetalElementType.image &&
            getPDFImagePathsWithElementId(element.id).length > enablePrintMax) {
          enablePrintMax = getPDFImagePathsWithElementId(element.id).length;
        }
      }
    } else if (templateDaMapInfo.isNotEmpty) {
      for (var element in templateDaMapInfo["elements"]) {
        if (element["type"] == "image" && getPDFImagePathsWithElementId(element["id"]).length > enablePrintMax) {
          enablePrintMax = getPDFImagePathsWithElementId(element["id"]).length;
        }
      }
    }

    return enablePrintMax;
  }

  ///获取PDF是否绑定了pdf
  bool templateIsBindPdf(TemplateData? templateData) {
    int enablePrintMax = getPDFEnablePrintMaxNumber(templateData);
    return enablePrintMax > 0;
  }

  ///获取当前绑定元素图片数组
  List<String> getPDFImagePathsWithElementId(String elementId) {
    Map<String, dynamic>? bindElementinfo = getPDFBindInfoByElementId(elementId);
    if (bindElementinfo?["pdfImagePaths"] != null) {
      return List<String>.from(bindElementinfo?["pdfImagePaths"]);
    } else {
      return [];
    }
  }

  ///获取当前绑定Pdf信息
  Map<String, dynamic>? getPDFBindInfoByElementId(String elementId) {
    Map<String, dynamic>? bindElementinfo;
    if (pdfBindElementInfos.isEmpty || elementId.isEmpty) return bindElementinfo;
    for (var element in pdfBindElementInfos) {
      if (element["bindedElementId"] == elementId) {
        bindElementinfo = element;
      }
    }
    return bindElementinfo;
  }

  ///元素是否绑定了pdf
  bool elementIsBindPdf(String elementId) {
    Map<String, dynamic>? bindElementinfo = getPDFBindInfoByElementId(elementId);
    return bindElementinfo != null;
  }
}

import 'package:niimbot_print_setting_plugin/model/data_source.dart';

class RfidBindInfo {
  List<DataSource> dataSources = [];
  String value = '';
  List<String> dataBind = [];

  int bindColumnIndex = -1;

  RfidBindInfo();

  bool isRfidContentLegal(String content) {
    final reg = RegExp(r'^([0-9a-fA-F]{4}){2,8}$');
    return reg.hasMatch(content);
  }

  int rowColumn2Index(String str) {
    String columnLetter = str.replaceAll(RegExp(r'[^a-zA-Z]'), '').toUpperCase();
    return letter2Index(columnLetter);
  }

  int letter2Index(String str) {
    int num = 0;
    for (int i = 0; i < str.length; i++) {
      num = num * 26 + (str.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }
    print(num);
    return num;
  }

  int getRealExcelPage({
    required bool customHeader,
    required int excelRowCount,
    required int subExcelPage,
    List<Range>? ranges,
  }) {
    if (excelRowCount == 0) {
      return subExcelPage;
    }

    List<Range> finalRanges;
    if (ranges == null || ranges.isEmpty) {
      finalRanges = [];
      if (customHeader) {
        finalRanges.add(Range(s: 1, e: excelRowCount));
      } else {
        finalRanges.add(Range(s: 2, e: excelRowCount + 1));
      }
    } else {
      finalRanges = ranges;
    }

    List<int> rowArray = [];
    for (var range in finalRanges) {
      for (int i = range.s; i <= range.e; i++) {
        if (!rowArray.contains(i)) {
          rowArray.add(i);
        }
      }
    }

    rowArray.sort();

    if (subExcelPage <= rowArray.length) {
      return rowArray[subExcelPage - 1];
    }

    return subExcelPage;
  }
}

class RfidContentParseData {
  final Map<String, dynamic> templateModule;
  final int subExcelPage;

  RfidContentParseData(this.templateModule, this.subExcelPage);
}

class RfidContentInfo {
  final String rfidContent;
  final bool isLegal;

  RfidContentInfo(this.rfidContent, this.isLegal);

  String getShowContent() {
    return isLegal ? rfidContent : 'app100001218';
  }
}

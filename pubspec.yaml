name: niimbot_print_setting_plugin
description: Flutter PrintSetting By Niimbot.
version: 1.0.0+1
author:
homepage: https://git.jc-ai.cn/print/foundation/niimbot_print_setting_plugin
publish_to: https://pub.niimbot.info

environment:
  sdk: "^3.3.0"
  flutter: ">=3.19.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  plugin_platform_interface: ^2.0.2
  get: 4.6.6
  flutter_svg: ^2.0.9
  fluttertoast: ^8.2.5
  keyboard_actions: 4.1.0
  dotted_border: ^2.1.0
  shared_preferences: ^2.0.15
  pull_to_refresh: ^2.0.0
  path_provider: ^2.0.6
  flutter_staggered_grid_view: ^0.6.2
#  netal_plugin: 1.2.32
  netal_plugin:
    git:
      url: 'https://git.jc-ai.cn/architect/dboard/netal_plugin.git'
      ref: '529903e1a74590a0d27c94e580c4a860bda8f033'
#      ref: 'feature/black_red_render'
  niimbot_excel: 1.1.15
#  niimbot_excel:
#    git:
#      url: https://git.jc-ai.cn/print/foundation/niimbot_excel.git
#      ref: feature/flutter_update_new
  # 图片处理、翻转、解码
  image: ^4.0.17
  niimbot_template:
    git:
      url: 'https://git.jc-ai.cn/print/foundation/niimbot_template.git'
      ref: '9fcbd9de0619fb674441aceb743689f49edcb418' # 6_3_2_c1_empty_values
#      path: '../../../plugins/niimbot_template'
#  niimbot_template:
#    path: '../../../plugins/niimbot_template'
#  nety:
#    path: 'nety'
#   nety: ^0.1.30
  nety:
  #  path: '../../../nety'
    git:
      url: 'https://git.jc-ai.cn/architect/dboard/nety.git'
      ref: '702178b135fb238024d1d098f7929c5b9f4a129a'
  flutter_easyloading: ^3.0.5
  collection: ^1.18.0
  # niimbot_print_strategy:
  #   path: '../../../niimbot_print_strategy'
  niimbot_print_strategy:
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_print_strategy.git
      ref: '5c8c4ec1884ce72ff3bb357bf451b39c2afb08ac'
  niimbot_mobile_ui: 1.0.5
#  niimbot_mobile_ui:
#    path: '../niimbot_mobile_ui'
  provider: ^6.1.2
  wakelock_plus: ^1.1.4
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' specifies the class (in Java, Kotlin, Swift, Objective-C, etc.)
  # which should be registered in the plugin registry. This is required for
  # using method channels.
  # The Android 'package' specifies package in which the registered class is.
  # This is required for using method channels on Android.
  # The 'ffiPlugin' specifies that native code should be built and bundled.
  # This is required for using `dart:ffi`.
  # All these are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.example.niimbot_print_setting_plugin
        pluginClass: NiimbotPrintSettingPlugin
      ios:
        pluginClass: NiimbotPrintSettingPlugin
      linux:
        pluginClass: NiimbotPrintSettingPlugin
      macos:
        pluginClass: NiimbotPrintSettingPlugin
      windows:
        pluginClass: NiimbotPrintSettingPluginCApi
      web:
        pluginClass: NiimbotPrintSettingPluginWeb
        fileName: niimbot_print_setting_plugin_web.dart

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

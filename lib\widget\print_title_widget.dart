import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';
import 'package:niimbot_template/models/template/label_name_info.dart';

class PrintTitleWidget extends StatefulWidget {
  PrintSettingLogic logic;

  PrintTitleWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintTitleWidgetState createState() => _PrintTitleWidgetState(logic);
}

class _PrintTitleWidgetState extends State<PrintTitleWidget> {
  PrintSettingLogic logic;
  String materialModelSn = "";
  _PrintTitleWidgetState(this.logic);

  Future<String> getSimpleInfo() async {
    // 构建基本信息字符串，包括设备尺寸
    String info = logic.getI18nString('app100000966', '自定义') +
        ": "
            "${logic.parameter!.templateMoudle?["width"]?.toDouble()}X${logic.parameter!.templateMoudle?["height"]?.toDouble()}";
    var cableLength = logic.parameter!.templateMoudle?["cableLength"]?.toInt() ?? 0;
    if (cableLength > 0) {
      info += "+$cableLength";
    }
    // 获取纸张类型名称，并添加到信息字符串中
    var paperType = logic.parameter!.templateMoudle?["paperType"].toString();
    String paperTypeName = logic.channel.getPaperTypeNameByCode(paperType.toString());
    info += "-$paperTypeName";
    // 初始化标签名称变量
    String labelName = "";
    String labelEnName = "";
    String labelCNName = '';
    // 解析并获取标签名称列表
    var labelNames = (logic.parameter!.templateMoudle?["labelNames"] as List<dynamic>)
        .map((e) => LabelNameInfo.fromJson(e))
        .toList();

    // 遍历标签名称列表，根据当前语言代码选择合适的标签名称
    for (LabelNameInfo labelNameInfo in labelNames) {
      if (logic.channel.getAppLanguageType() == labelNameInfo.languageCode) {
        labelName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "en") {
        labelEnName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "zh-cn") {
        labelCNName = labelNameInfo.name;
      }
    }
    // 根据当前语言选择合适的标签名称，如果为空，则选择英文或中文名称
    // 当前语言> EN > CN
    labelName = labelName.isEmpty ? (labelEnName.isEmpty ? labelCNName : labelEnName) : labelName;
    // 如果存在标签ID，则使用标签名称作为信息字符串
    var labelId = logic.extrain["labelId"];
    if (labelId?.isNotEmpty == true) {
      info = logic.getI18nString("app100001731", '标签纸：\$', param: [labelName]);
    }

    Map printerConnect = await logic.channel.getPrinterLabelData() ?? {};
    if (printerConnect.isNotEmpty) {
      materialModelSn = logic.extrain['materialModelSn'] ?? "";
      if (printerConnect['profile']?['extrain']?['labelId'] == labelId) {
        info = logic.getI18nString("app100000683", '当前识别') + ": $labelName";
      } else {
        info = "";
      }
    }
    if (info.isEmpty) {
      info = "discrepancy";
    }
    return info;
  }

  @override
  void initState() {
    super.initState();
    logic.isMultipleTemplates();
    getSimpleInfo().then((value) {
      logic.style.labelName = value;
      logic.update([UpdateFieldManager.title]);
    });
    logic.stream.listen((data) {
      if (data["action"] == "setFlutterlabelData" || data['action'] == 'replaceLabelEvent') {
        Future.delayed(Duration(milliseconds: 1000), () {
          getSimpleInfo().then((value) {
            logic.style.labelName = value;
            logic.update([UpdateFieldManager.title]);
          });
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double horizontalPadding = 0;
    double verticalPadding = 0;
    if(logic.taskType != PrintTaskType.miniApp) {
      horizontalPadding = 10;
      verticalPadding = 5;
    }
    return Column(
      children: [
        Container(
          padding: EdgeInsetsDirectional.fromSTEB(16 - horizontalPadding, MediaQuery.of(context).padding.top + 12 - verticalPadding, 16, 11 - verticalPadding),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.0),
              topRight: Radius.circular(12.0),
            ),
          ),
          child: Stack(
            children: [
              Align(
                alignment: AlignmentDirectional.centerStart,
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsetsDirectional.symmetric(horizontal: horizontalPadding, vertical: verticalPadding),
                    color: Colors.transparent,
                    child: SvgIcon(
                      logic.taskType == PrintTaskType.miniApp ?logic.style.titleIcon : logic.style.titleIconLeft,
                      matchTextDirection: true,
                    ),
                  ),
                ),
              ),
              Align(
                alignment: AlignmentDirectional.center,
                child: Container(
                  padding: EdgeInsetsDirectional.only(top: 0),
                  child: Text(
                    logic.style.title,
                    style: logic.style.titleStyle,
                  ),
                ),
              ),
              SizedBox()
            ],
          ),
        ),
        logic.taskType == PrintTaskType.miniApp ? SizedBox() : _buildLabelInfoWidget()
      ],
    );
  }

  Widget _buildLabelInfoWidget() {
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.title,
        builder: (logic) {
          return Container(
            height: 30,
            padding: EdgeInsets.symmetric(vertical: 4.5, horizontal: 16),
            decoration: BoxDecoration(color: logic.style.labelBgColor),
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                logic.style.labelName != "discrepancy"
                    ? Container(
                        width: 180,
                        child: Text(logic.style.labelName,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: true,
                            style: logic.style.labelNameStyle),
                      )
                    : Container(
                        width: 180,
                        child: Row(
                          children: [
                            SvgIcon(
                              logic.style.sizeToastIcon,
                              matchTextDirection: true,
                            ),
                            SizedBox(
                              width: 3,
                            ),
                            Container(
                              width: 150,
                              child: Text(logic.style.sizeToastTitle,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: true,
                                  style: logic.style.sizeToastStyle),
                            )
                          ],
                        ),
                      ),
                Expanded(child: Container()),
                Offstage(
                  offstage: materialModelSn.isEmpty,
                  child: Text(" ID:" + materialModelSn,
                      maxLines: 1, overflow: TextOverflow.ellipsis, softWrap: true, style: logic.style.labelNameStyle),
                ),
              ],
            ),
          );
        });
  }
}

# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.6.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.11.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  change:
    dependency: transitive
    description:
      name: change
      sha256: "65db7f966dc7e786687f49900a94c5f08b0eb9ca8c4a3e7eed3a55e980b455e2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.4"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  cider:
    dependency: transitive
    description:
      name: cider
      sha256: dfff70e9324f99e315857c596c31f54cb7380cfa20dfdfdca11a3631e05b7d3e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.8"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.18.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "876849631b0c7dc20f8b471a2a03142841b482438e3b707955464f5ffca3e4c3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.0"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.8"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.10"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "24a261d5d5c87e86c7651c417a5dbdf8bcd7080dd592533910e8d0505a279f21"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "282d3cf731045a2feb66abfe61bbc40870ae50a3ed10a4d3d217556c35c8c2ba"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.1"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "5598aa796bbf4699afd5c67c0f5f6e2ed542afc956884b9cd58c306966efc260"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.7.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "33259a9276d6cea88774a0000cfae0d861003497755969c92faa223108620dc8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  dotted_border:
    dependency: transitive
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  enum_to_string:
    dependency: transitive
    description:
      name: enum_to_string
      sha256: "93b75963d3b0c9f6a90c095b3af153e1feccb79f6f08282d3274ff8d9eea52bc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.5"
  excel:
    dependency: transitive
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.6"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.1"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_easyloading:
    dependency: transitive
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  flutter_hooks:
    dependency: transitive
    description:
      name: flutter_hooks
      sha256: "6a126f703b89499818d73305e4ce1e3de33b4ae1c5512e3b8eab4b986f46774c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.18.6"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_net_signature:
    dependency: transitive
    description:
      name: flutter_net_signature
      sha256: "41061328952c181878d694c1be2d6dc4bfb383268a52efe6e0563d024944e0ce"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.12"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.1"
  flutter_staggered_grid_view:
    dependency: transitive
    description:
      name: flutter_staggered_grid_view
      sha256: "1312314293acceb65b92754298754801b0e1f26a1845833b740b30415bbbcf07"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.2"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: "578bd8c508144fdaffd4f77b8ef2d8c523602275cd697cc3db284dbd762ef4ce"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.14"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_xlider:
    dependency: transitive
    description:
      name: flutter_xlider
      sha256: b83da229b8a2153adeefc5d9e08e0060689c8dc2187b30e3502cf67c1a6495be
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.5.0"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      sha256: "95f349437aeebe524ef7d6c9bde3e6b4772717cf46a0eb6a3ceaddc740b297cc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.2.8"
  get:
    dependency: transitive
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.6.6"
  gql:
    dependency: transitive
    description:
      name: gql
      sha256: "650e79ed60c21579ca3bd17ebae8a8c8d22cde267b03a19bf3b35996baaa843a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1-alpha+1730759315362"
  gql_dedupe_link:
    dependency: transitive
    description:
      name: gql_dedupe_link
      sha256: "10bee0564d67c24e0c8bd08bd56e0682b64a135e58afabbeed30d85d5e9fea96"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.4-alpha+1715521079596"
  gql_dio_link:
    dependency: transitive
    description:
      name: gql_dio_link
      sha256: "0a38185eb2eeabcc8d81c8322b8b6da9057af36553c7acb46e54fe035866ccef"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1+1"
  gql_error_link:
    dependency: transitive
    description:
      name: gql_error_link
      sha256: "93901458f3c050e33386dedb0ca7173e08cebd7078e4e0deca4bf23ab7a71f63"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0+1"
  gql_exec:
    dependency: transitive
    description:
      name: gql_exec
      sha256: "394944626fae900f1d34343ecf2d62e44eb984826189c8979d305f0ae5846e38"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1-alpha+1699813812660"
  gql_http_link:
    dependency: transitive
    description:
      name: gql_http_link
      sha256: ef6ad24d31beb5a30113e9b919eec20876903cc4b0ee0d31550047aaaba7d5dd
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  gql_link:
    dependency: transitive
    description:
      name: gql_link
      sha256: c2b0adb2f6a60c2599b9128fb095316db5feb99ce444c86fb141a6964acedfa4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1-alpha+1730759315378"
  gql_transform_link:
    dependency: transitive
    description:
      name: gql_transform_link
      sha256: "0645fdd874ca1be695fd327271fdfb24c0cd6fa40774a64b946062f321a59709"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  graphql:
    dependency: transitive
    description:
      name: graphql
      sha256: b90f3faa525fed0d8b57f528af913cf1363e1d77f287004d3a15ce699fa866ee
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.0-beta.9"
  graphql_flutter:
    dependency: transitive
    description:
      name: graphql_flutter
      sha256: "2423b394465e7d83a5e708cd2f5b37b54e7ae9900abfbf0948d512fa46961acb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.0-beta.7"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.3"
  http:
    dependency: transitive
    description:
      name: http
      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.3.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.19.0"
  isar:
    dependency: transitive
    description:
      name: isar
      sha256: "99165dadb2cf2329d3140198363a7e7bff9bbd441871898a87e26914d25cf1ea"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isar_flutter_libs:
    dependency: transitive
    description:
      name: isar_flutter_libs
      sha256: bc6768cc4b9c61aabff77152e7f33b4b17d2fc93134f7af1c3dd51500fe8d5e8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isolate:
    dependency: transitive
    description:
      name: isolate
      sha256: "3554ab10fdeec965d27e0074c913ccb2229887633da080d2b35a6322da14938b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.9.0"
  keyboard_actions:
    dependency: transitive
    description:
      name: keyboard_actions
      sha256: d621d15d7626303798e451008a223642a86978d06fc4adff7461d77859834aa9
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "3f87a60e8c63aecc975dda1ceedbc8f24de75f09e4856ea27daf8958f2f0ce05"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.0.5"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "932549fb305594d82d7183ecd9fa93463e9914e1b67cacc34bc40906594a1806"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "0a217c6c989d21039f1498c3ed9f3ed71b354e69873f13a8dfc3c9fe76f1b452"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.7.0"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "935e23e1ff3bc02d390bad4d4be001208ee92cc217cb5b5a6c19bc14aaa318c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.3.0"
  marker:
    dependency: transitive
    description:
      name: marker
      sha256: "3dadd01f3b0ffae148ffb3b1bc04290a98e54a465cddbab59727bd2a9fe57750"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.15.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.6"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  netal_plugin:
    dependency: transitive
    description:
      path: "."
      ref: "1b1d331ca5fae73fd515e3a23688b6598e02bb7e"
      resolved-ref: "1b1d331ca5fae73fd515e3a23688b6598e02bb7e"
      url: "https://git.jc-ai.cn/architect/dboard/netal_plugin.git"
    source: git
    version: "1.2.32"
  nety:
    dependency: transitive
    description:
      path: "."
      ref: "41d814a520d313457ed264ff120749744ef79f1c"
      resolved-ref: "41d814a520d313457ed264ff120749744ef79f1c"
      url: "https://git.jc-ai.cn/architect/dboard/nety.git"
    source: git
    version: "0.1.30"
  niimbot_excel:
    dependency: transitive
    description:
      name: niimbot_excel
      sha256: b8bf7069631b90cfb451a2394f1a1cd450495d0aaf1f79dddbac77fee6edce95
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.4"
  niimbot_http:
    dependency: transitive
    description:
      name: niimbot_http
      sha256: e8199340564084fcd85a8cf360189f799cb879daef803cf4ebba6692d4b23a96
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.36"
  niimbot_intl:
    dependency: transitive
    description:
      name: niimbot_intl
      sha256: "7ed1b66cf1815042cecc9a64cdb9f8001370b1628e350952d1c6ee4793234c19"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.5"
  niimbot_local_storage:
    dependency: transitive
    description:
      name: niimbot_local_storage
      sha256: c9548a81b989dfe139be5321f80889c6f727e4fc9cf84fdb51f4c8c796e7a200
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.4"
  niimbot_mobile_ui:
    dependency: transitive
    description:
      name: niimbot_mobile_ui
      sha256: "7d024eb02c0c76b2e7114fee3dc54feab5ea212e35fa662891eab67c75643fdd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.2"
  niimbot_print_setting_plugin:
    dependency: "direct main"
    description:
      path: ".."
      relative: true
    source: path
    version: "1.0.0+1"
  niimbot_print_strategy:
    dependency: transitive
    description:
      path: "."
      ref: e267a2f6de4471e1728ac133f430ec041bf02655
      resolved-ref: e267a2f6de4471e1728ac133f430ec041bf02655
      url: "https://git.jc-ai.cn/print/foundation/niimbot_print_strategy.git"
    source: git
    version: "0.0.15"
  niimbot_template:
    dependency: transitive
    description:
      path: "."
      ref: "feature/app_templateManager"
      resolved-ref: af7a904fddae0ca068c9b994a94a0eec130436d3
      url: "https://git.jc-ai.cn/print/foundation/niimbot_template.git"
    source: git
    version: "0.1.45"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.5.0"
  normalize:
    dependency: transitive
    description:
      name: normalize
      sha256: "8a60e37de5b608eeaf9b839273370c71ebba445e9f73b08eee7725e0d92dbc43"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.8.2+1"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: da8d9ac8c4b1df253d1a328b7bf01ae77ef132833479ab40763334db13b91cce
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.1.1"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: ac1f4a4847f1ade8e6a87d1f39f5d7c67490738642e2542f559ec38c37489a66
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "6f01f8e37ec30b07bc424b4deabac37cacb1bc7e2e515ad74486039918a37eb7"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.10"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.0"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.0.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.9.1"
  protobuf:
    dependency: transitive
    description:
      name: protobuf
      sha256: "68645b24e0716782e58948f8467fd42a880f255096a821f9e7d0ec625b00c84d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "81876843eb50dc2e1e5b151792c9a985c5ed2536914115ed04e9c8528f6647b0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.0"
  pull_to_refresh:
    dependency: transitive
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: cb808fb6f1a839e6fc5f7d8cb3b0a10e1db48b3be102de73938c627f0b636336
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.3"
  rfc_6901:
    dependency: transitive
    description:
      name: rfc_6901
      sha256: df1bbfa3d023009598f19636d6114c6ac1e0b7bb7bf6a260f0e6e6ce91416820
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5c3004a4a8dbb94bd4bf5412a4def4acdaa12e12f269737a5751369e12d1a962"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.28.0"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "95f9997ca1fb9799d494d0cb2a780fd7be075818d59f00c43832ed112b158a82"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "480ba4345773f56acda9abf5f50bd966f581dac5d514e5fc4a18c62976bbba7e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.2"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "07e050c7cd39bad516f8d64c455f04508d09df104be326d8c02551590a0d513d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.3"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d2ca4132d3946fec2184261726b355836a82c33d7d5b67af32692aff18a4684e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5b8a98dafc4d5c4c9c72d8b31ab2b23fc13422348d2997120294d3bac86b4ddb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "773c9522d66d523e1c7b25dfb95cc91c26a1e17b107039cfe147285e92de7878"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.14"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "2430b973a4ca3c4dbc9999b62b8c719a160100dcbae5c819bae0cacce32c9cdb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.12"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: ab9ff38fc771e9ee1139320adbe3d18a60327370c218c60752068ebee4b49ab1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.15"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.4"
  version_manipulation:
    dependency: transitive
    description:
      name: version_manipulation
      sha256: e90782d610bde19765d2808ec06bc8ed9e04640a4dd07d1a3d370728ce9dae7f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.0"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "14.2.5"
  web:
    dependency: transitive
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "68d1e89a91ed61ad9c370f9f8b6effed9ae5e0ede22a270bdfa6daf79fc2290a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.5.4"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.4.0 <4.0.0"
  flutter: ">=3.22.0"

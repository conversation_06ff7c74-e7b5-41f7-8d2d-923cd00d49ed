import 'package:flutter/material.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';

/// 抽象类：用于定义打印组件的构建接口
abstract class PrintWidgetBuildInterface {
  /// 设置标题组件
  setTitleWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget);

  /// 设置预览组件
  setPreviewWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget);

  /// 设置设备选择组件
  setDeviceWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget);

  /// 设置设置组件
  setSettingWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget);

  /// 设置底部按钮组件
  setBottomBtnWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget);
}

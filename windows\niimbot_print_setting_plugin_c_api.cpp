#include "include/niimbot_print_setting_plugin/niimbot_print_setting_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "niimbot_print_setting_plugin.h"

void NiimbotPrintSettingPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  niimbot_print_setting_plugin::NiimbotPrintSettingPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}

import 'package:flutter/services.dart';

class MaxLengthNumberFormatter extends TextInputFormatter {
  final int maxLength;

  MaxLengthNumberFormatter(this.maxLength);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // 只允许数字
    final newString = newValue.text;

    // 过滤掉非数字字符
    final filteredString = newString.replaceAll(RegExp(r'[^0-9]'), '');

    // 限制长度
    final truncatedString = filteredString.length > maxLength ? filteredString.substring(0, maxLength) : filteredString;

    // 返回格式化后的值
    return newValue.copyWith(
      text: truncatedString,
      selection: TextSelection.collapsed(offset: truncatedString.length),
    );
  }
}

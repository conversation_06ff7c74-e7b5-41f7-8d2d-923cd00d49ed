import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';

class CanvasBottomBtnWidget extends StatefulWidget {
  PrintSettingLogic logic;

  CanvasBottomBtnWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _CanvasBottomBtnWidgetState createState() => _CanvasBottomBtnWidgetState(logic);
}

class _CanvasBottomBtnWidgetState extends State<CanvasBottomBtnWidget> {
  PrintSettingLogic logic;

  _CanvasBottomBtnWidgetState(this.logic);

  static double bottomHeight = 0;

  @override
  void initState() {
    super.initState();
    logic.stream.listen((event) {
      if (event["action"] == "saveUpdateCanvasData") {
        int currentPage = logic.parameter?.templateMoudle?["currentPage"]??1;
        logic.parameter?.templateMoudle = jsonDecode(event["saveSuccessData"]);
        logic.parameter?.templateMoudle?["currentPage"] = currentPage;
      } else if (event["action"] == "refreshTemplate") {
        logic.isMultipleTemplates();
        if (logic.style.isShowRange) {
          logic.channel.trackEvent("show", "024_067_095");
        }
      }
    });

    WidgetsBinding.instance.endOfFrame.then(
      (value) {
        if (mounted) {
          bottomHeight = MediaQuery.of(context).padding.bottom;
        }
      },
    );
    logic.channel.trackEvent("show", "024_067_096");
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.printButton,
        builder: (logic) {
          return Container(
            padding: EdgeInsetsDirectional.only(bottom: bottomHeight),
            margin: EdgeInsets.only(top: MediaQuery.of(context).viewInsets.bottom / 1.2),
            color: Colors.white,
            child: Column(
              children: [
                Divider(
                  height: 1,
                  thickness: 0.5,
                  color: Color(0xFFEBEBEB),
                ),
                SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          logic.toPrint(isSave: true);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44,
                          decoration: BoxDecoration(
                            color: logic.style.printAndSaveBtnBgColor,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          margin: EdgeInsetsDirectional.fromSTEB(16, 0, 5, 0),
                          child: logic.style.isSavePrinting
                              ? Container(
                                  alignment: Alignment.center,
                                  child: const CupertinoActivityIndicator(
                                    radius: 13,
                                    animating: true,
                                  ),
                                )
                              : Text(
                                  logic.style.printAndSaveBtnTitle,
                                  style: logic.style.printAndSaveBtnStyle,
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          logic.toPrint();
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44,
                          decoration: BoxDecoration(
                            color: logic.style.printBtnBgColor,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          margin: EdgeInsetsDirectional.fromSTEB(5, 0, 16, 0),
                          child: logic.style.isPrinting
                              ? Container(
                                  alignment: Alignment.center,
                                  child: const CupertinoActivityIndicator(
                                    radius: 13,
                                    color: Colors.white,
                                    animating: true,
                                  ),
                                )
                              : Text(
                                  logic.style.printBtnTitle,
                                  style: logic.style.printBtnStyle,
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                logic.style.isShowRange
                    ? GestureDetector(
                        onTap: () {
                          logic.toPrint(isJustOne: true);
                        },
                        child: Container(
                          child: Text(
                            logic.style.printJustOneBtnTitle,
                            style: logic.style.printJustOneBtnStyle,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    : Container(),
                Container(
                  color: Colors.transparent,
                  height: bottomHeight > 0 ? 0 : 20,
                )
              ],
            ),
          );
        });
  }
}

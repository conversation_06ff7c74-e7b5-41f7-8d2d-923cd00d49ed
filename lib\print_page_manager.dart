import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/interface/print_widget_build_interface.dart';
import 'package:niimbot_print_setting_plugin/model/print_page_style.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/widget/batch_bottom_btn_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/canvas_bottom_btn_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/print_bottom_btn_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/print_device_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/print_preview_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/print_setting_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/print_title_widget.dart';

class PrintPageManager implements PrintWidgetBuildInterface {
  PrintPageStyle style = PrintPageStyle();
  final String Function(String stringCode, String defaultStr, {List<String>? param})? getI18nString;
  late Widget Function(PrintSettingLogic logic) printPageItems;
  Widget Function(BuildContext context, PrintSettingLogic logic)? titleWidget;
  Widget Function(BuildContext context, PrintSettingLogic logic)? previewWidget;
  Widget Function(BuildContext context, PrintSettingLogic logic)? deviceWidget;
  Widget Function(BuildContext context, PrintSettingLogic logic)? settingWidget;
  Widget Function(BuildContext context, PrintSettingLogic logic)? bottomBtnWidget;
  BuildContext context;
  String? capId;

  PrintPageManager(this.context, this.getI18nString);

  build(PrintTaskType taskType) {
    style.title = getI18nString!("app00019", "打印设置");
    style.bindDataError = getI18nString!("app100001167", "打印设置");
    style.deviceTitle = getI18nString!("app00272", "设备型号");
    style.rfidSoureUnCompliance = getI18nString!("app100001218", "不符合规范");
    style.rfidSoureUnBind = getI18nString!("app100001230", "RFID未绑定");
    style.deviceMsgTitle = getI18nString!("app100001733", "更多信息");
    style.offsetTitle = getI18nString!("app00977", "偏移校准");
    style.printModelTitle = getI18nString!("app100001170", "打印模式");
    style.sizeToastTitle = getI18nString!("app100001519", "与当前识别不匹配");
    style.printDensityTitle = getI18nString!("app00488", "打印浓度");
    style.printAndSaveBtnTitle = getI18nString!("app100001585", "保存并打印");
    style.printBtnTitle = getI18nString!("app00016", "打印");
    style.printJustOneBtnTitle = getI18nString!("app100001102", "打印一张试试");
    style.liveCodeErrorTitle = getI18nString!("app100000602", "您已删除模板中高级二维码/表单");
    style.carbonTitle = getI18nString!("app100000069", "当前碳带颜色为");
    style.defaultTextValue = getI18nString!("app00364", "双击编辑");
    // 不需要打印设置UI
    if (taskType == PrintTaskType.printNullUi ||
        taskType == PrintTaskType.printC1 ||
        taskType == PrintTaskType.printNullUiShowProgress) {
      printPageItems = (PrintSettingLogic logic) {
        return Container();
      };
    } else {
      printPageItems = (PrintSettingLogic logic) {
        return _buildWidget(taskType, logic);
      };
    }
  }

  _buildWidget(PrintTaskType taskType, PrintSettingLogic logic) {
    switch (taskType) {
      case PrintTaskType.miniApp:
        Map<String, dynamic>? customData = logic.parameter!.customData;
        Map<dynamic, dynamic>? uniAppInfo = customData?["uniAppInfo"];
        capId = uniAppInfo?["uniAppId"];
        String themeColor = uniAppInfo?["themeColor"] ?? "#fb4b42";
        Color color = hexToColor(themeColor);
        style.printBtnBgColor = color;
        style.saveBtnBgColor = color;
        style.connectDialogColor = color;
        style.printSettingThemeColor = color;
        break;
      default:
        style.printBtnBgColor = const Color(0xFFFB4B42);
        style.saveBtnBgColor = const Color(0xFFFB4B42);
        style.printSettingThemeColor = const Color(0xFFFB4B42);
        break;
    }

    switch (taskType) {
      case PrintTaskType.canvas:
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildTitleWidget(logic),
            Expanded(
              child: ListView(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.all(0),
                children: [
                  _buildPreviewWidget(logic),
                  _buildDeviceWidget(logic),
                  _buildSettingWidget(logic),
                ],
              ),
            ),
            CanvasBottomBtnWidget(logic)
          ],
        );
      case PrintTaskType.batch:
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildTitleWidget(logic),
            Expanded(
              child: ListView(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.all(0),
                children: [
                  _buildPreviewWidget(logic),
                  _buildDeviceWidget(logic),
                  _buildSettingWidget(logic),
                ],
              ),
            ),
            BatchBottomBtnWidget(logic)
          ],
        );
      case PrintTaskType.miniApp:
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildTitleWidget(logic),
            Expanded(
              child: ListView(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.all(0),
                children: [
                  //todo 危废小程序显示预览图
                  capId == "__CAP__SPR666G" ? _buildPreviewWidget(logic) : const SizedBox.shrink(),
                  _buildDeviceWidget(logic),
                  _buildSettingWidget(logic),
                ],
              ),
            ),
            _buildBottomBtnWidget(logic)
          ],
        );
      default:
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildTitleWidget(logic),
            Expanded(
              child: ListView(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.all(0),
                children: [
                  _buildPreviewWidget(logic),
                  _buildDeviceWidget(logic),
                  _buildSettingWidget(logic),
                ],
              ),
            ),
            _buildBottomBtnWidget(logic)
          ],
        );
    }
  }

  Color hexToColor(String hexString) {
    // 确保字符串以 '#' 开头
    if (!hexString.startsWith('#')) {
      throw FormatException('颜色字符串必须以 # 开头');
    }

    // 去掉 '#' 并将字符串转换为大写
    final buffer = StringBuffer();
    if (hexString.length == 7) {
      // 如果是 #RRGGBB 格式，添加透明度 FF
      buffer.write('FF');
    }
    buffer.write(hexString.replaceFirst('#', '').toUpperCase());

    // 解析为 ARGB 格式
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  _buildPreviewWidget(PrintSettingLogic logic) {
    return previewWidget == null ? PrintPreviewWidget(logic) : previewWidget;
  }

  _buildTitleWidget(PrintSettingLogic logic) {
    return titleWidget == null ? PrintTitleWidget(logic) : titleWidget;
  }

  _buildDeviceWidget(PrintSettingLogic logic) {
    return deviceWidget == null ? PrintDeviceWidget(logic) : deviceWidget;
  }

  _buildSettingWidget(PrintSettingLogic logic) {
    return settingWidget == null ? PrintSettingWidget(logic) : settingWidget;
  }

  _buildBottomBtnWidget(PrintSettingLogic logic) {
    return bottomBtnWidget == null ? PrintBottomBtnWidget(logic) : bottomBtnWidget;
  }

  @override
  setTitleWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget) {
    titleWidget = widget;
  }

  @override
  setBottomBtnWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget) {
    bottomBtnWidget = widget;
  }

  @override
  setDeviceWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget) {
    deviceWidget = widget;
  }

  @override
  setPreviewWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget) {
    previewWidget = widget;
  }

  @override
  setSettingWidget(Widget Function(BuildContext context, PrintSettingLogic logic) widget) {
    settingWidget = widget;
  }
}

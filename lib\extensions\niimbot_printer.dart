import 'package:niimbot_print_setting_plugin/print/print_manager.dart';
import '../print/model/print_device_model.dart';
import 'package:nety/models/niimbot_bluetooth_printer.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_wifi_printer.dart';

extension NiimbotPrinterExtension on NiimbotPrinter {
  /// 获取打印机名称
  String? get name {
    if (this is NiimbotWIFIPrinter) {
      return (this as NiimbotWIFIPrinter).name;
    }
    if (this is NiimbotBluetoothPrinter) {
      return (this as NiimbotBluetoothPrinter).name;
    }
    return null;
  }

  /// 获取设备唯一标识
  String? get machineId {
    if (code != null) {
      final deviceModel = PrintManager().printSettingLogic.channel.getCurrentPrinterInfo(code.toString(), name ?? '');
      if (deviceModel?.name.isEmpty ?? true) {
        return '${sn?.toUpperCase()}';
      }
      return '${deviceModel!.name}${deviceModel.name.contains('-') ? '' : '-'}${sn?.toUpperCase()}';
    }
    if (name != null) {
      return name;
    }
    return sn?.toUpperCase();
  }

  /// 设备机型
  PrintDeviceModel? get deviceModel {
    final deviceModel = PrintManager().printSettingLogic.channel.getCurrentPrinterInfo(code.toString(), name ?? '');
    return deviceModel;
  }

  int? get seriesId {
    if (deviceModel != null) {
      return int.tryParse(deviceModel!.seriesId);
    }
    return null;
  }
}

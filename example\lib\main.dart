import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _platformVersion = 'Unknown';
  String test =
      "{\"batchGoodsList\":[],\"hasGoodsTemplate\":false,\"niimbotGoodsInfo\":{},\"niimbotTemplate\":{\"background\":\"\",\"backgroundImage\":\"https://oss-print-fat.jc-test.cn/public_resources/labels/153880781-5bfb768f316e7c77906fbb0d0ae68d7c.png\",\"backgroundWidth\":592,\"bindDataSource\":true,\"bindDataSourceOnlyOneRow\":false,\"bindInfo\":{\"page\":1,\"total\":3},\"cableDirection\":-1,\"cableLength\":0,\"canvasRotate\":0,\"cloudTemplate\":false,\"cloudTemplateId\":\"\",\"commodityTemplate\":true,\"consumableType\":1,\"consumableTypeTextId\":\"app01230\",\"contentThumbnail\":\"\",\"currentPage\":1,\"dataSource\":[{\"hash\":\"b45e3c62cf66431ca03f7c95d4f5cc77\",\"headers\":{},\"name\":\"\",\"params\":{\"ids\":[\"10072909\",\"10072908\",\"10072885\"]},\"range\":[],\"type\":\"commodity\",\"uri\":\"\"}],\"description\":\"\",\"elements\":[{\"boxStyle\":\"auto-width\",\"centerInCanvas\":false,\"colorChannel\":0,\"colorReverse\":0,\"contentTitle\":\"\",\"dataBind\":[],\"delimiter\":\"：\",\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontCode\":\"ZT001\",\"fontFamily\":\"ZT001\",\"fontSize\":4.2,\"fontStyle\":[],\"hasVipRes\":false,\"height\":5.251142,\"hint\":\"\",\"id\":\"3e833445-1006-4575-8c6b-c40725c09a38\",\"ignoreFields\":[],\"isLock\":0,\"isOpenMirror\":false,\"letterSpacing\":0,\"lineBreakMode\":0,\"lineMode\":2,\"lineSpacing\":0,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textAlignHorizonral\":1,\"textAlignVertical\":0,\"textStyle\":[\"norm\"],\"title\":false,\"type\":\"text\",\"typesettingMode\":1,\"typesettingParam\":[0,180],\"value\":\"绿\",\"width\":5.194064,\"wordSpacing\":0,\"x\":22.402967,\"y\":17.12,\"zIndex\":0},{\"codeType\":20,\"colorChannel\":0,\"colorReverse\":0,\"dataBind\":[],\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontSize\":3.2,\"hasVipRes\":false,\"height\":10,\"id\":\"4fc95664ae9c443c9897e42b2768e925\",\"isLock\":0,\"isOpenMirror\":false,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textHeight\":3.4,\"textPosition\":0,\"type\":\"barcode\",\"value\":\"123456\",\"width\":20,\"x\":22.402967,\"y\":22.37114,\"zIndex\":0},{\"boxStyle\":\"auto-width\",\"centerInCanvas\":false,\"colorChannel\":0,\"colorReverse\":0,\"contentTitle\":\"品名\",\"dataBind\":[\"\",\"commodity\"],\"delimiter\":\"：\",\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontCode\":\"ZT001\",\"fontFamily\":\"ZT001\",\"fontSize\":3.2,\"fontStyle\":[],\"hasVipRes\":false,\"height\":3.995434,\"hint\":\"\",\"id\":\"8c428226b0354115b2f1c13e746cfe69\",\"ignoreFields\":[],\"isLock\":0,\"isOpenMirror\":false,\"letterSpacing\":0,\"lineBreakMode\":0,\"lineMode\":2,\"lineSpacing\":0,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textAlignHorizonral\":0,\"textAlignVertical\":0,\"textStyle\":[\"norm\"],\"title\":false,\"type\":\"text\",\"typesettingMode\":1,\"typesettingParam\":[],\"value\":\"{0⊙1}\",\"width\":22.659817,\"wordSpacing\":0,\"x\":22.402967,\"y\":32.37114,\"zIndex\":0},{\"boxStyle\":\"auto-width\",\"centerInCanvas\":false,\"colorChannel\":0,\"colorReverse\":0,\"contentTitle\":\"零售价\",\"dataBind\":[\"\",\"commodity\"],\"delimiter\":\"：\",\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontCode\":\"ZT001\",\"fontFamily\":\"ZT001\",\"fontSize\":3.2,\"fontStyle\":[],\"hasVipRes\":false,\"height\":3.995434,\"hint\":\"\",\"id\":\"b3c0bb2f492c4d3ca2459be9027b9419\",\"ignoreFields\":[],\"isLock\":0,\"isOpenMirror\":false,\"letterSpacing\":0,\"lineBreakMode\":0,\"lineMode\":2,\"lineSpacing\":0,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textAlignHorizonral\":0,\"textAlignVertical\":0,\"textStyle\":[\"norm\"],\"title\":false,\"type\":\"text\",\"typesettingMode\":1,\"typesettingParam\":[],\"value\":\"{0⊙6}\",\"width\":17.237444,\"wordSpacing\":0,\"x\":22.402967,\"y\":36.37114,\"zIndex\":0},{\"boxStyle\":\"auto-width\",\"centerInCanvas\":false,\"colorChannel\":0,\"colorReverse\":0,\"contentTitle\":\"规格\",\"dataBind\":[\"\",\"commodity\"],\"delimiter\":\"：\",\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontCode\":\"ZT001\",\"fontFamily\":\"ZT001\",\"fontSize\":3.2,\"fontStyle\":[],\"hasVipRes\":false,\"height\":3.995434,\"hint\":\"\",\"id\":\"35970def06094e5fb421350f30f4fd74\",\"ignoreFields\":[],\"isLock\":0,\"isOpenMirror\":false,\"letterSpacing\":0,\"lineBreakMode\":0,\"lineMode\":2,\"lineSpacing\":0,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textAlignHorizonral\":0,\"textAlignVertical\":0,\"textStyle\":[\"norm\"],\"title\":false,\"type\":\"text\",\"typesettingMode\":1,\"typesettingParam\":[],\"value\":\"{0⊙4}\",\"width\":14.041096,\"wordSpacing\":0,\"x\":22.402967,\"y\":40.37114,\"zIndex\":0},{\"boxStyle\":\"auto-width\",\"centerInCanvas\":false,\"colorChannel\":0,\"colorReverse\":0,\"contentTitle\":\"产地\",\"dataBind\":[\"\",\"commodity\"],\"delimiter\":\"：\",\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontCode\":\"ZT001\",\"fontFamily\":\"ZT001\",\"fontSize\":3.2,\"fontStyle\":[],\"hasVipRes\":false,\"height\":3.995434,\"hint\":\"\",\"id\":\"f5365970fb734871a49669f240f6a43b\",\"ignoreFields\":[],\"isLock\":0,\"isOpenMirror\":false,\"letterSpacing\":0,\"lineBreakMode\":0,\"lineMode\":2,\"lineSpacing\":0,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textAlignHorizonral\":0,\"textAlignVertical\":0,\"textStyle\":[\"norm\"],\"title\":false,\"type\":\"text\",\"typesettingMode\":1,\"typesettingParam\":[],\"value\":\"{0⊙2}\",\"width\":14.041096,\"wordSpacing\":0,\"x\":22.402967,\"y\":44.37114,\"zIndex\":0},{\"codeType\":24,\"colorChannel\":0,\"colorReverse\":0,\"dataBind\":[\"\",\"commodity\"],\"elementColor\":[255,0,0,0],\"fieldName\":\"\",\"fontSize\":3.2,\"hasVipRes\":false,\"height\":10,\"id\":\"6044fe272b1e450988c164650c6b6174\",\"isLock\":0,\"isOpenMirror\":false,\"mirrorId\":\"\",\"paperColorIndex\":0,\"rotate\":0,\"textHeight\":3.4,\"textPosition\":0,\"type\":\"barcode\",\"value\":\"{0⊙0}\",\"width\":26,\"x\":22.402967,\"y\":48.37114,\"zIndex\":0}],\"emptyGoods\":false,\"excel_id\":\"\",\"externalData\":{\"fileName\":\"\",\"id\":\"0\",\"list\":[{\"data\":{\"columnHeaders\":[\"商品条码\",\"品名\",\"产地\",\"单位\",\"规格\",\"等级\",\"零售价\",\"促销价\",\"物价员\"],\"columns\":[[\"948895200016\",\"6939095200016\",\"\"],[\"948895200016\",\"复方芩兰口服液\",\"测试。。。\"],[\"\",\"\",\"\"],[\"\",\"\",\"\"],[\"\",\"每支装10毫升\",\"\"],[\"\",\"\",\"\"],[\"\",\"\",\"\"],[\"\",\"\",\"\"],[\"\",\"\",\"\"]]},\"name\":\"\"}],\"useTitle\":false},\"formIds\":\"\",\"fromIndustry\":false,\"fromOldVersion\":0,\"goodsBarCode\":\"\",\"goodsListInPrintTask\":[],\"goodsTemplate\":true,\"hasVIPRes\":false,\"height\":40,\"id\":\"152224\",\"isCable\":false,\"isCheck\":false,\"isEdited\":0,\"is_com\":0,\"is_move\":0,\"is_need_move\":false,\"label\":false,\"labelId\":\"\",\"labelNames\":[{\"languageCode\":\"zh-cn\",\"languageName\":\"简体中文\",\"name\":\"精臣标签纸R50*40-180白色B款\"},{\"languageCode\":\"zh-cn-t\",\"languageName\":\"繁体中文\",\"name\":\"R50*40-180白色B款\"},{\"languageCode\":\"en\",\"languageName\":\"English\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"ja\",\"languageName\":\"日本語\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"ru\",\"languageName\":\"русский\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"it\",\"languageName\":\"Italiano\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"fr\",\"languageName\":\"Français\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"de\",\"languageName\":\"Deutsche\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"es\",\"languageName\":\"Español\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"ko\",\"languageName\":\"한국어\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"Polish\",\"languageName\":\"Polskie\",\"name\":\"\"},{\"languageCode\":\"Czech\",\"languageName\":\"Čeština\",\"name\":\"\"},{\"languageCode\":\"th\",\"languageName\":\"泰语\",\"name\":\"\"},{\"languageCode\":\"pt\",\"languageName\":\"葡萄牙语\",\"name\":\"\"},{\"languageCode\":\"af\",\"languageName\":\"荷兰语\",\"name\":\"\"},{\"languageCode\":\"ind\",\"languageName\":\"印度尼西亚\",\"name\":\"\"},{\"languageCode\":\"ar\",\"languageName\":\"阿拉伯语\",\"name\":\"\"},{\"languageCode\":\"WHH\",\"languageName\":\"武汉话\",\"name\":\"\"},{\"languageCode\":\"tr\",\"languageName\":\"土耳其语\",\"name\":\"\"},{\"languageCode\":\"ms\",\"languageName\":\"马来语\",\"name\":\"\"},{\"languageCode\":\"hi\",\"languageName\":\"印地语\",\"name\":\"\"}],\"layoutSchema\":\"\",\"liveCodeIds\":\"\",\"localBackground\":[\"/data/user/0/com.gengcon.android.jccloudprinter/files/jc/zh-cn/background/background_152224_1723702541237.png\"],\"localContentThumb\":\"\",\"local_cloud_id\":\"\",\"local_private\":0,\"local_thumb\":\"\",\"local_type\":-1,\"margin\":[0,0,0,0],\"modify\":{\"35970def06094e5fb421350f30f4fd74\":{\"0\":{\"delimiter\":\"：\",\"useTitle\":false}},\"8c428226b0354115b2f1c13e746cfe69\":{\"0\":{\"delimiter\":\"：\",\"useTitle\":false}},\"b3c0bb2f492c4d3ca2459be9027b9419\":{\"0\":{\"delimiter\":\"：\",\"useTitle\":false}},\"f5365970fb734871a49669f240f6a43b\":{\"0\":{\"delimiter\":\"：\",\"useTitle\":false}}},\"multipleBackIndex\":0,\"name\":\"未命名模板\",\"names\":[{\"languageCode\":\"zh-cn\",\"languageName\":\"简体中文\",\"name\":\"精臣标签纸R50*40-180白色B款\"},{\"languageCode\":\"zh-cn-t\",\"languageName\":\"繁体中文\",\"name\":\"R50*40-180白色B款\"},{\"languageCode\":\"en\",\"languageName\":\"English\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"ja\",\"languageName\":\"日本語\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"ru\",\"languageName\":\"русский\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"it\",\"languageName\":\"Italiano\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"fr\",\"languageName\":\"Français\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"de\",\"languageName\":\"Deutsche\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"es\",\"languageName\":\"Español\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"ko\",\"languageName\":\"한국어\",\"name\":\"R50*40-180White\"},{\"languageCode\":\"Polish\",\"languageName\":\"Polskie\",\"name\":\"\"},{\"languageCode\":\"Czech\",\"languageName\":\"Čeština\",\"name\":\"\"},{\"languageCode\":\"th\",\"languageName\":\"泰语\",\"name\":\"\"},{\"languageCode\":\"pt\",\"languageName\":\"葡萄牙语\",\"name\":\"\"},{\"languageCode\":\"af\",\"languageName\":\"荷兰语\",\"name\":\"\"},{\"languageCode\":\"ind\",\"languageName\":\"印度尼西亚\",\"name\":\"\"},{\"languageCode\":\"ar\",\"languageName\":\"阿拉伯语\",\"name\":\"\"},{\"languageCode\":\"WHH\",\"languageName\":\"武汉话\",\"name\":\"\"},{\"languageCode\":\"tr\",\"languageName\":\"土耳其语\",\"name\":\"\"},{\"languageCode\":\"ms\",\"languageName\":\"马来语\",\"name\":\"\"},{\"languageCode\":\"hi\",\"languageName\":\"印地语\",\"name\":\"\"}],\"originTemplateId\":\"153880781\",\"paccuracy\":8,\"paccuracyName\":203,\"paperColor\":[\"0.0.0\",\"230.0.18\"],\"paperType\":1,\"platformCode\":\"CP001Mobile\",\"previewImage\":\"\",\"printedProcessList\":[],\"profile\":{\"barcode\":\"03222123\",\"extrain\":{\"adaptPlatformCode\":\"CP001Mobile,CP001PC,CP001Pad,CP002Mobile,CP002PC,CP002Pad\",\"adaptPlatformName\":\"精臣云打印-移动端,精臣云打印-pc端,精臣云打印-平板,臣印-移动端,臣印-pc端,臣印-平板\",\"amazonCodeBeijing\":\"\",\"amazonCodeWuhan\":\"\",\"barcodeCategoryMap\":{\"barcode\":\"03222123\"},\"clickNum\":0,\"commodityCategoryId\":\"0\",\"createTime\":\"2024-06-13 11:06:31\",\"downloadCount\":0,\"folderId\":\"0\",\"goodsCode\":\"\",\"industryId\":\"0\",\"isCustom\":false,\"isDelete\":false,\"isHot\":false,\"isMobileTemplete\":true,\"isNewPath\":false,\"isPrivate\":false,\"labelId\":\"153880781\",\"materialModelSn\":\"\",\"phone\":\"\",\"resourceVersion\":\"5.10.0\",\"sortDependency\":\"0\",\"sourceId\":\"153880781\",\"sparedCode\":\"\",\"templateClass\":1,\"templateType\":2,\"updateTime\":\"2024-08-15 14:09:41\",\"userId\":\"54828\",\"virtualBarCode\":\"\"},\"hardwareSeriesId\":\"\",\"keyword\":\"\",\"machineId\":\"49\",\"machineName\":\"B203\"},\"rotate\":0,\"show_background\":\"\",\"supportedEditors\":[\"GENERAL\"],\"task\":{\"externalDataID\":\"\",\"modifyData\":{}},\"templatePrintMode\":-1,\"templateVersion\":\"*******\",\"thumbnail\":\"https://oss-print-fat.jc-test.cn/user_resources/0f4d1022b88a11eb9302a683e7b21ab9/54828/templates/1823965268578275328.png\",\"totalPage\":3,\"usedFonts\":{\"ZT001\":\"ZT001.ttf\",\"fontDefault\":\"ZT001.ttf\"},\"version\":\"3.0.0\",\"vip\":false,\"width\":50,\"x\":0,\"y\":0}}";

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
          appBar: AppBar(
            title: const Text('Plugin example app'),
          ),
          body: Builder(
            builder: (context) => OutlinedButton(
              onPressed: () {},
              child: Text('Running on: $_platformVersion\n'),
            ),
          )),
    );
  }
}

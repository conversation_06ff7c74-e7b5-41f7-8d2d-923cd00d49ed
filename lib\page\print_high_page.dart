import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/interface/print_setting_channel_interface.dart';
import 'package:niimbot_print_setting_plugin/model/print_paramter.dart';
import 'package:niimbot_print_setting_plugin/print_page_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class PrintHighPage extends StatefulWidget {
  PrintTaskType taskType;
  PrintParameter? parameter;
  PrintPageManager pageManager;
  PrintSettingChannelInterface channel;

  PrintHighPage(this.taskType, this.parameter, this.pageManager, this.channel, {Key? key}) : super(key: key);

  @override
  _PrintHighPageState createState() => _PrintHighPageState();
}

class _PrintHighPageState extends State<PrintHighPage> {
  late PrintSettingLogic logic;

  @override
  void initState() {
    super.initState();
    logic = PrintSettingLogic(widget.taskType, widget.parameter, widget.pageManager, widget.channel);
    debugPrint("打印设置模板数据进入" + DateTime.now().millisecondsSinceEpoch.toString());
    logic.addListenerEventBus((result) {
      if (!result) {
        if (!Platform.isWindows) {
          Fluttertoast.showToast(
              msg: logic.pageManager.style.bindDataError,
              gravity: ToastGravity.CENTER,
              backgroundColor: const Color(0xCC333333),
              textColor: Colors.white);
        }
        Future.delayed(const Duration(seconds: 2), () {
          Navigator.pop(context);
        });
      }
    });
    Get.put(logic);
    if (widget.taskType == PrintTaskType.miniApp) {
      logic.channel.trackEvent("view", "55");
    } else {
      logic.channel.trackEvent("view", "024");
    }
    logic.channel.trackEvent("view", "134");

    logic.stream.listen((data) {
      if (data.containsKey("networkChanged")) {
        bool netConnected = data['networkChanged'];
        //检查离线弹窗逻辑
        if (mounted) {
          logic.channel.checkOfflinePeriod(context);
        }
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 初次进入打印设置页面 检查离线弹窗逻辑
      logic.channel.checkOfflinePeriod(context);
    });

    // 打开保活（防止设备熄屏）
    WakelockPlus.enable();
  }

  @override
  void dispose() {
    super.dispose();
    // 页面销毁时关闭 wakelock
    WakelockPlus.disable();
    logic.destroyEventBus();
    Get.delete<PrintSettingLogic>();
  }

  @override
  Widget build(BuildContext context) {
    logic.initGenerateDesignRatio(context);
    return Material(
        child: GestureDetector(
      onTap: () {
        if (Platform.isIOS) {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
            FocusManager.instance.primaryFocus?.unfocus();
          }
        }
        debugPrint("拦截点击空白位置发生的页面重绘");
      },
      child: PopScope(
        canPop: ModalRoute.of(context)?.isCurrent == true,
        child: Container(
            color: widget.taskType == PrintTaskType.printNullUi ? Colors.transparent : Color(0xFFF7F7FA),
            child: logic.pageManager.printPageItems(logic)),
      ),
    ));
  }
}

import 'dart:async';

import 'package:flutter/material.dart';

class AttrIconButton extends StatefulWidget {
  final Widget icon;
  final VoidCallback? onTap;

  /// tap返回且带有Context,存在此属性时不返回onTap;
  final ValueChanged? onTapContext;

  final VoidCallback? onLongPress;
  final VoidCallback? onTapCancel;
  final GestureTapDownCallback? onTapDown;
  final GestureTapUpCallback? onTapUp;
  final double width;
  final double height;
  final BorderRadius borderRadius;

  /// 是否启用长按节流
  final isEnableLongPressThrottle;

  /// 触发时间
  final Duration? duration;

  const AttrIconButton(
    this.icon, {
    super.key,
    this.width = 40,
    this.height = 40,
    this.borderRadius = const BorderRadius.all(Radius.circular(4)),
    this.onTap,
    this.onLongPress,
    this.onTapUp,
    this.onTapDown,
    this.onTapCancel,
    this.onTapContext,
    this.isEnableLongPressThrottle = true,
    this.duration,
  });

  @override
  State<AttrIconButton> createState() => _AttrIconButtonState();
}

class _AttrIconButtonState extends State<AttrIconButton> {
  Timer? timer;

  void onJobEnd() {
    timer?.cancel();
    timer = null;
  }

  void onJobStart(VoidCallback action) {
    if (timer != null) return;
    timer = Timer.periodic(widget.duration ?? Duration(milliseconds: 200), (timer) {
      final action = widget.onLongPress ?? widget.onTap;
      if (action == null) {
        onJobEnd();
      } else {
        action();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onLongPressStart: (_) {
          if (widget.isEnableLongPressThrottle) {
            final action = widget.onLongPress ?? widget.onTap;
            if (action != null) {
              onJobStart(action);
            }
          } else {
            widget.onLongPress?.call();
          }
        },
        onLongPressEnd: (_) {
          if (widget.isEnableLongPressThrottle) {
            onJobEnd();
          }
        },
        onTapUp: widget.onTapUp,
        onTap: () {
          if (widget.onTapContext != null) {
            widget.onTapContext?.call(context);
          } else if (widget.onTap != null) {
            widget.onTap?.call();
          }
        },
        onTapDown: widget.onTapDown,
        onTapCancel: widget.onTapCancel,
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(borderRadius: widget.borderRadius),
          child: Center(
            child: widget.icon,
          ),
        ),
      ),
    );
  }
}

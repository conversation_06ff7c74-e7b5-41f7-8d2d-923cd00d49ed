import 'package:flutter/material.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_template/models/template_data.dart';

/// 打印设置通道接口，定义了与打印设置相关的各种操作
abstract class PrintSettingChannelInterface {
  /// 添加事件总线监听器
  void addEventBusListener(Function listener);

  /// 销毁事件总线
  void destroyEventBus();

  /// 根据类型代码获取纸张类型名称
  String getPaperTypeNameByCode(String type);

  /// 获取应用语言类型
  String getAppLanguageType();

  /// 获取打印机标签数据
  Future<Map?> getPrinterLabelData();

  /// 获取连接设备信息
  Future<Map?> getConnectDevice();

  /// 同步打印设置中的RFID替换功能
  void syncPrintSettingRfidReplace();

  /// 获取打印机系列信息
  Future<String> getPrinterSeriesInfo(String machineId);

  /// 设置设备偏移量
  void setDeviceOffset(num offsetY, num offsetX);

  /// 打开连接页面
  void openConnectPage({bool fromUnimp = false, String uniappId = ""});

  /// 获取打印设置信息
  Future<List> getPrintSettingMsg(materialModelSn);

  /// 设置打印密度
  void setPrintDensity(int density);

  /// 获取当前打印密度信息
  Future<Map> getCurrentPrintDensityInfo(String consumableCode);

  /// 获取当前打印机信息
  getCurrentPrinterInfo(String hardCode, String printerName);

  /// 获取当前打印机的机器ID
  String getCurrentPrinterMachineId(String hardCode, String printerName, String printerSN);

  /// 检查批量打印的源
  Future<int> checkBatchPrintSources(List<Map<String, dynamic>> batchIds);

  /// 保存数据
  Future<void> toSave(TemplateData? data);

  /// 检查活码是否被删除
  Future<bool> liveCodeIsDelete(Map<String, dynamic> templateJson);

  /// 获取是否显示RFID的设置
  Future<bool> getIsShowRfid();

  /// 获取模板数据表头
  Future<List> getTemplateRowDataHeaderWith(Map<String, dynamic> dataSource);

  /// 获取模板详情
  Future<Map<String, dynamic>> getTemplateDetail(String id);

  /// 将模板转换为标准格式
  Future<Map<String, dynamic>> transformTemplateToStand(Map<String, dynamic> templateMap);

  /// 重置生成设计比例
  void resetGenerateDesignRatio(BuildContext context, double templateWidth, double templateHeight);

  /// 检查是否连接设备
  bool isConnected();

  /// 检查是否登录
  bool isLogin();

  /// 显示更改打印数据的界面
  void showChangePrintData(BuildContext context, String templateJsonStr, int jumpType);

  /// 发送事件
  void postEvent(Map<String, dynamic> eventData);

  /// 获取标签替换标志
  bool getLabelReplaceFlag();

  /// 跟踪事件
  void trackEvent(String eventType, String eventCode, {Map<String, dynamic> eventData});

  /// 处理打印错误事件回调
  Future<int> unUIPrintErrorEventCallback(Map<String, dynamic> errorInfo);

  /// 刷新连接设备信息
  void refreshConnectDevice(NiimbotPrinter deviceName) {}

  /// 获取设备RFID缓存信息
  List<NiimbotRFIDInfo> getDeviceRFIDCacheInfos();

  /// 检查当前纸张是否支持设备
  bool currentPaperIsSupportDevice();

  /// 获取纸张和色带颜色信息
  Map<String, String> getPaperRibbonColor();

  /// 设置无需UI打印完成
  void setUnNeedUIPrintComplete(String status, String uniAppId, String taskId, String message);

  /// 保存打印记录
  void savePrintRecord(
      String uniqueValue,
      TemplateData template,
      int printNum,
      int lastPrintCount,
      PrintTaskType taskType,
      double printCardPaperLength,
      double lastRibbonLength,
      String uniappId,
      bool isConnected,
      String printStrategy,
      String illegalCode,
      Function function) {}

  /// 风险检查
  void riskCheck(BuildContext context, int action, {List<NiimbotRFIDInfo>? rfidInfos});

  /// 获取批量打印模板
  Future<Map<String, dynamic>> getBatchPrintTemplate(String templateId);

  /// 从C1获取模版数据
  Future<TemplateData?> getBatchPrintC1Template(int templateIndex);

  /// 下载模板详情
  void downloadTemplateDetails(List<Map<String, dynamic>> batchIds);

  /// 去应用市场评分
  void toMarketRating({String? uniAppId = ""});

  /// 保存历史记录
  void saveHistory(String deviceType, String copies, String uniqueId, bool isBatchPrint, TemplateData templateData,
      {Map? templateMap, int page = 1, bool isSupportGray16 = false, bool isPrintHistory = false});

  /// 保存内容
  void saveContent(TemplateData templateData, String uniqueValue);

  /// 刷新服务中的RFID信息
  void refreshRFIDInfoInService(Map<String, dynamic> serviceRFIDInfo);

  /// 上传打印数据日志
  void uploadPrintDataLog(String uniqueValue, Function function);

  /// 标签记录
  void labelRecord(Map<String, dynamic> templateData, String uniAppId);

  /// 保存打印设备日志
  void savePrintDeviceLog();

  /// 查看危废台账
  void checkDangerRecord();

  /// 向小程序发送生成预览图事件
  void sendCapGenerateTemplatePreviewEvent(String imageData);

  /// 获取打印盲区
  List<double> getPrintArea(double templateWidth, double templateHeight, String consumableType, String paperType,
      int rotate, int canvasWidth, int cableDirection, double cableLength, int canvasRotate);

  ///更新用户打印张数
  void updateUserPrintCount(int printNum);

  /// 更新小程序用户打印次数
  void updateUserMiniProgramPrintCount(int printNum, {String? uniAppId = ""});

  ///是否触发nps弹窗
  Future<bool> isTriggerNpsPop();

  /// 是否触发小程序nps弹窗
  Future<bool> isTriggerMiniProgramNpsPop({String? uniAppId = ""});

  ///弹出nps
  void showNps();

  /// 弹出小程序nps
  void showMiniProgramNps({String? uniAppId = ""});

  /// 获取RFID替换的模板
  Future<TemplateData> getRfidReplaceTemplate(TemplateData target, TemplateData source);

  ///打印设置页侧滑返回控制
  void setFlutterVCCanSideslip(bool value);

  ///获取连接打印机别名
  String getConnectedMachineAlias();

  ///检查离线周期，返回是否可以打印
  Future<bool> checkOfflinePeriod(BuildContext context);

  ///未登录情况下，保存显示暂存本地弹窗
  void showStageDialog(BuildContext context, bool isCanSavaLocal, Function()? loginSucceed);

  ///C1打印过程中堵管异常
  void tubeExceptionCallback();
}

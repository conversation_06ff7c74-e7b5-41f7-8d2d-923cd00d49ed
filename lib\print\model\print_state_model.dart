import 'package:collection/collection.dart';
import 'package:nety/exceptions/niimbot_nety_exception.dart';
import 'package:nety/models/index.dart';

import '../print_manager.dart';

//打印过程状态描述类
class PrintStateModel<T> {
  final T value;
  final String title;
  final String? content;

  const PrintStateModel({
    required this.value,
    required this.title,
    this.content,
  });

  static PrintStateModel? buildStatus(
      {int? errorCode = 0,
      NiimbotPrinterPrintStatus? printStatus,
      NiimbotPrinterStatus? printerStatus,
      NiimbotNetyExceptionCode? printError,
      bool isC1Data = false}) {
    List<PrintStateModel<int>> printModalList = getPrintModalList();
    List<PrintStateModel<NiimbotPrinterPrintStatus>> printStatusList = getPrintStatusList();
    PrintStateModel? result = printStatusList.firstWhereOrNull((e) => e.value == printStatus);
    if (printerStatus == NiimbotPrinterStatus.disconnected) {
      result = PrintStateModel(
          value: 1,
          title: PrintManager().getI18nString('app00743', '打印机已断开'),
          content: PrintManager().getI18nString('app01262', '请重新连接打印机打印'));
    }
    if (printError != null) {
      if ([NiimbotNetyExceptionCode.busy, NiimbotNetyExceptionCode.fail].contains(printError)) {
        result = printModalList.firstWhereOrNull((e) => e.value == 9);
      }
      if (printError == NiimbotNetyExceptionCode.unSupport) {
        result = printModalList.firstWhereOrNull((e) => e.value == 16);
      }
    }
    if (errorCode != 0) {
      result = printModalList.firstWhereOrNull((e) => e.value == errorCode) ??
          PrintStateModel(
              value: 255,
              title: PrintManager().getI18nString('app01201', '打印异常'),
              content: PrintManager().getI18nString('pc0146', '请重启打印机后再试；如未解决，请联系客服'));
    }
    if (isC1Data) {
      if (result?.value == 2) {
        result = PrintStateModel(
            value: 2,
            title: PrintManager().getI18nString('app100001539', '套管已用完，请及时更换'));
      } else if (result?.value == 8) {
        result = PrintStateModel(
            value: 8,
            title: PrintManager().getI18nString('app100001540', '请检查线管有无异常或碳带是否用完'));
      } else if (result?.value == 15) {
        result = PrintStateModel(
            value: 15,
            title: PrintManager().getI18nString('app100001540', '请检查线管有无异常或碳带是否用完'));
      }
    }
    return result;
  }

  static List<PrintStateModel<NiimbotPrinterPrintStatus>> getPrintStatusList() {
    List<PrintStateModel<NiimbotPrinterPrintStatus>> printStatusList = [
      PrintStateModel(
        value: NiimbotPrinterPrintStatus.init,
        title: PrintManager().getI18nString('app01163', '准备打印...'),
        content: PrintManager().getI18nString('pc0096', '请勿断开设备连接或退出软件。'),
      ),
      PrintStateModel(
          value: NiimbotPrinterPrintStatus.ready,
          title: PrintManager().getI18nString('app01163', '正在打印...'),
          content: PrintManager().getI18nString('pc0096', '请勿断开设备连接或退出软件。')),
      PrintStateModel(
          value: NiimbotPrinterPrintStatus.printing,
          title: PrintManager().getI18nString('app01110', '正在打印...'),
          content: PrintManager().getI18nString('pc0096', '请勿断开设备连接或退出软件。')),
      PrintStateModel(
        value: NiimbotPrinterPrintStatus.pausing,
        title: PrintManager().getI18nString('app01265', '打印暂停'),
      ),
      PrintStateModel(
        value: NiimbotPrinterPrintStatus.completed,
        title: PrintManager().getI18nString('app01438', '打印完成'),
      ),
      PrintStateModel(
        value: NiimbotPrinterPrintStatus.completed,
        title: PrintManager().getI18nString('app01438', '打印完成'),
      ),
    ];
    return printStatusList;
  }

  static List<PrintStateModel<int>> getPrintModalList() {
    List<PrintStateModel<int>> printModalList = [
      PrintStateModel(
          value: -1,
          title: PrintManager().getI18nString('pc0103', '打印异常，请重新打印'),
          content: PrintManager().getI18nString('pc0104', '请重启打印机后再试；如未解决，请联系客服')),
      PrintStateModel(
          value: 1,
          title: '【1】${PrintManager().getI18nString('app00624', '盒盖打开')}',
          content: PrintManager().getI18nString('app01253', '请关闭盒盖')),
      PrintStateModel(
          value: 2,
          title: '【2】${PrintManager().getI18nString('app00625', '缺纸')}',
          content: PrintManager().getI18nString('app01254', '请放入纸张')),
      PrintStateModel(
          value: 3,
          title: '【3】${PrintManager().getI18nString('app00626', '电量不足')}',
          content: PrintManager().getI18nString('app01255', '请充电')),
      PrintStateModel(
          value: 4,
          title: '【4】${PrintManager().getI18nString('app00627', '电池异常')}',
          content: PrintManager().getI18nString('app01256', '请联系客服')),
      PrintStateModel(value: 5, title: '【5】${PrintManager().getI18nString('app00628', '已停止打印')}'),
      PrintStateModel(value: 6, title: '【6】${PrintManager().getI18nString('app00629', '数据错误')}'),
      PrintStateModel(value: 7, title: '【7】${PrintManager().getI18nString('app00630', '打印头温度过高')}'),
      PrintStateModel(
          value: 8,
          title: '【8】${PrintManager().getI18nString('app00631', '出纸异常')}',
          content: PrintManager().getI18nString('app01257', '请检查纸张')),
      PrintStateModel(value: 9, title: '【9】${PrintManager().getI18nString('app01200', '打印机忙碌')}'),
      PrintStateModel(
          value: 10,
          title: '【10】${PrintManager().getI18nString('app00611', '未检测到打印头')}',
          content: PrintManager().getI18nString('app01256', '请联系客服')),
      PrintStateModel(value: 11, title: '【11】${PrintManager().getI18nString('app00618', '环境温度过低')}'),
      PrintStateModel(value: 12, title: '【12】${PrintManager().getI18nString('app00615', '打印头未锁紧')}'),
      PrintStateModel(value: 13, title: '【13】${PrintManager().getI18nString('app00616', '未检测到碳带')}'),
      PrintStateModel(value: 14, title: '【14】${PrintManager().getI18nString('app100001440', '碳带不匹配')}'),
      PrintStateModel(
        value: 15,
        title: '【15】${PrintManager().getI18nString('app00619', '碳带已用完')}',
      ),
      PrintStateModel(
          value: 16,
          title: '【16】${PrintManager().getI18nString('app01258', '走纸类型错误')}',
          content: PrintManager().getI18nString('app01259', '请修改走纸类型')),
      PrintStateModel(
          value: 17,
          title: '【17】${PrintManager().getI18nString('app01258', '走纸类型错误')}',
          content: PrintManager().getI18nString('app01259', '请修改走纸类型')),
      PrintStateModel(value: 18, title: '【18】${PrintManager().getI18nString('app01251', '打印模式错误')}'),
      PrintStateModel(value: 19, title: '【19】${PrintManager().getI18nString('app01252', '打印浓度错误')}'),
      PrintStateModel(value: 20, title: '【20】${PrintManager().getI18nString('app01260', 'RFID写入失败')}'),
      PrintStateModel(value: 21, title: '【21】${PrintManager().getI18nString('app00629', '数据错误')}'),
      PrintStateModel(
          value: 22,
          title: '【22】${PrintManager().getI18nString('app01261', '通讯异常')}',
          content: PrintManager().getI18nString('app01262', '请重新连接')),
      PrintStateModel(
          value: 23,
          title: '【23】${PrintManager().getI18nString('app00743', '打印机断开')}',
          content: PrintManager().getI18nString('app01262', '请重新连接')),
      PrintStateModel(
          value: 24,
          title: '【24】${PrintManager().getI18nString('app01264', '模板错误')}',
          content: PrintManager().getI18nString('app01263', '请重新编辑模板')),
      PrintStateModel(
          value: 25,
          title: '【25】${PrintManager().getI18nString('app01264', '模板错误')}',
          content: PrintManager().getI18nString('app01263', '请重新编辑模板')),
      PrintStateModel(
          value: 26,
          title: '【26】${PrintManager().getI18nString('app01264', '模板错误')}',
          content: PrintManager().getI18nString('app01263', '请重新编辑模板')),
      PrintStateModel(value: 27, title: '【27】${PrintManager().getI18nString('app00631', '出纸异常')}'),
      PrintStateModel(
          value: 28,
          title: '【28】${PrintManager().getI18nString('app01258', '走纸类型错误')}',
          content: PrintManager().getI18nString('app01259', '请修改走纸类型')),
      PrintStateModel(value: 29, title: '【29】${PrintManager().getI18nString('app01260', 'RFID写入失败')}'),
      PrintStateModel(value: 30, title: '【30】${PrintManager().getI18nString('app01252', '打印浓度错误')}'),
      PrintStateModel(value: 31, title: '【31】${PrintManager().getI18nString('app01251', '打印模式错误')}'),
      PrintStateModel(value: 32, title: '【32】${PrintManager().getI18nString('app01245', '耗材材质不匹配')}'),
      PrintStateModel(value: 33, title: '【33】${PrintManager().getI18nString('app01245', '耗材材质不匹配')}'),
      PrintStateModel(value: 34, title: '【34】${PrintManager().getI18nString('app100001017', '打印机响应超时')}'),
      PrintStateModel(value: 35, title: '【35】${PrintManager().getI18nString('app100001678', '切刀异常')}'),
      PrintStateModel(
          value: 36,
          title: '【36】${PrintManager().getI18nString('app00625', '缺纸')}',
          content: PrintManager().getI18nString('app01254', '请放入纸张')),
      PrintStateModel(value: 37, title: '【37】${PrintManager().getI18nString('app100001679', '打印机异常')}'),
      PrintStateModel(
        value: 38,
        title: PrintManager().getI18nString('app100001195', 'RFID来源不符合规范'),
      ),
      PrintStateModel(
        value: 50,
        title: '【50】${PrintManager().getI18nString('app100001062', '非法标签')}',
      ),
      PrintStateModel(value: 51, title: PrintManager().getI18nString('app100001063', '非法标签和碳带')),
      PrintStateModel(
          value: 52,
          title: '【52】${PrintManager().getI18nString('pc0256', '固件接收数据超时')}',
          content: PrintManager().getI18nString('pc0257', '请稍后再试')),
      PrintStateModel(value: 53, title: PrintManager().getI18nString('pc0241', '请使用精臣正版碳带')),
      PrintStateModel(value: 54, title: PrintManager().getI18nString('app100001541', '线号机电量低，请接通电源')),
      PrintStateModel(value: 55, title: PrintManager().getI18nString('app100001542', '堵管，请开盖检查。')),
      PrintStateModel(
          value: 255,
          title: PrintManager().getI18nString('pc0145', '未知错误'),
          content: PrintManager().getI18nString('pc0109', '请重启打印机后再试；如果解决，请联系客服')),
      PrintStateModel(
        value: 10000,
        title: PrintManager().getI18nString('app100001195', 'RFID来源不符合规范'),
      ),
    ];
    return printModalList;
  }
}

import 'package:niimbot_print_setting_plugin/model/print_data.dart';

/// C1 打印任务的各种参数和配置信息
class C1PrintData extends PrintData {
  /// 切割类型(C1线号机使用)  0 - 不切  1 - 分割线  2 - 半切
  int? cutType;

  /// 耗材类型（C1线号机使用）
  int? tubeType;

  /// 耗材类型的直径（C1线号机使用）
  double? tubeSpecs;

  /// 是否半切
  bool? isHalfCut;

  /// 半切深度
  int? cutDepth;

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      ...super.toJsonObject(),
      "cutType": cutType,
      "tubeType": tubeType,
      "tubeSpecs": tubeSpecs,
      "isHalfCut": isHalfCut,
      "cutDepth": cutDepth
    };
  }
}


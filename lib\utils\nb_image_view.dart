import 'dart:typed_data';

import 'package:flutter/material.dart';

class NBImageView extends StatelessWidget {
  final Future<Uint8List?> future;
  final int canvasRotate;
  bool isBatchPreview = false;
  NBImageView(this.future, this.canvasRotate, {super.key,this.isBatchPreview = false});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done && (snapshot.data?.isNotEmpty ?? false)) {
          Uint8List imageData = snapshot.data!;
          return Image.memory(
            scale: isBatchPreview?0.1:1,
            imageData,
            /** 避免闪动 */
            gaplessPlayback: true,
            fit: BoxFit.contain,
          );
        } else {
          return Container(
            height: 100,
            decoration:
                const BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.all(Radius.circular(10))),
          );
        }
      },
    );
  }
}

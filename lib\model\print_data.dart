import 'dart:convert';

/// 打印任务的各种参数和配置信息。
class PrintData {
  /// printTemplate 是一个可选的字符串，代表打印的模板。
  String? printTemplate;

  /// printCount 定义了需要打印的数量，默认为 1。
  int printCount = 1;

  /// printDensity 定义了打印密度，用于控制打印质量，默认值为 0。
  int printDensity = 0;

  /// pageBegin 指定了打印的起始页码，默认为 1。
  int pageBegin = 1;

  /// pageEnd 指定了打印的结束页码，默认为 1。
  int pageEnd = 1;

  /// printPriority 表示打印任务的优先级，默认为 0。
  int printPriority = 0;

  /// ofsetY 定义了在 Y 轴上的打印偏移量，单位为毫米。
  double ofsetY = 0;

  /// ofsetX 定义了在 X 轴上的打印偏移量，单位为毫米。
  double ofsetX = 0;

  /// batchtIds 用于存储批量打印任务的标识符，以逗号分隔。
  String batchtIds = "";

  /// isSave 标识是否保存模板，默认为 false。
  bool isSave = false;

  /// printHistoryId 存储打印历史记录的唯一标识符。
  String printHistoryId = "";

  /// 表示是否显示 RFID 绑定信息。
  bool? showRfidBind;

  /// rfidInfo 是一个可选的字符串，存储 RFID 相关的信息。
  String? rfidInfo;

  /// taskType 用于指定打印任务的类型。
  String taskType = "";

  /// uniappId 是一个可选的字符串，用于标识打印任务所属的统一应用 ID。
  String? uniappId = "";

  bool isJustOne = false;

  bool isShowLoading = true;

  /// batchGoodsList 存储待打印的商品列表，默认为空列表。
  List batchGoodsList = [];

  Map<String, dynamic> toJsonObject() {
    Map<String, dynamic> data = {
      'printCount': printCount,
      'printDensity': printDensity,
      'pageBegin': pageBegin,
      'pageEnd': pageEnd,
      'printPriority': printPriority,
      'ofsetY': ofsetY,
      'ofsetX': ofsetX,
      'batchtIds': batchtIds,
      'isSave': isSave,
      'printHistoryId': printHistoryId,
      'taskType': taskType,
      'showRfidBind': showRfidBind,
      'rfidInfo': rfidInfo,
      'uniappId': uniappId
    };

    // 处理可能为 null 的字段
    if (printTemplate != null) {
      data['printTemplate'] = printTemplate;
    }

    // 处理可能为空的列表
    if (batchGoodsList.isNotEmpty) {
      data['batchGoodsList'] = batchGoodsList;
    }
    return data;
  }

  String toJson() {
    Map<String, dynamic> data = toJsonObject();
    return jsonEncode(data);
  }
}

import 'package:flutter/material.dart';

class PrintPageStyle {
  // 定义弹窗标题文本，默认为空
  String title = '';
  // 定义弹窗标题的文本样式，包括颜色、字体大小和字体权重
  TextStyle? titleStyle = TextStyle(color: Color(0xFF161616), fontSize: 18.0, fontWeight: FontWeight.w600);
  // 定义弹窗标题的背景颜色，默认为白色
  Color titleBgColor = Colors.white;
  // 定义弹窗标题图标路径，默认为一个关闭图标
  String titleIcon = 'assets/close.svg';
  String titleIconLeft = 'assets/close_left.svg';

  // 数据源数据异常提示
  String bindDataError = '';
  // 定义标签名称文本，默认为空
  String labelName = '';
  // 定义标签名称的文本样式，包括颜色、字体大小和字体权重
  TextStyle? labelNameStyle = TextStyle(color: Color(0x993C3C43), fontSize: 12.0, fontWeight: FontWeight.w400);
  TextStyle? liveCodeErrorStyle = TextStyle(color: Color(0xFFFF3B30), fontSize: 12.0, fontWeight: FontWeight.w400);
  TextStyle? carbonStyle = TextStyle(color: Color(0xFF999999), fontSize: 12.0, fontWeight: FontWeight.w400);
  TextStyle? previewTtileStyle = TextStyle(color: Color(0xFF161616), fontSize: 12.0, fontWeight: FontWeight.w400);
  // 定义标签的背景颜色，默认为白色
  Color labelBgColor = Colors.white;
  String switchLeftIcon = 'assets/switch_left.svg';
  Color switchLeftColor = Color(0x3C3C434D);
  String switchRightIcon = 'assets/switch_right.svg';
  String liveCodeErrorIcon = 'assets/error.svg';
  String previewDownArrowIcon = 'assets/down_arrow.svg';
  String paperArrowIcon = 'assets/paper_arrow.svg';
  String rfidIcon = 'assets/rfid_mark_small.svg';
  String rfidWarningIcon = 'assets/rfid_warn.svg';
  String wifiIcon = 'assets/wifi_icon.svg';
  String arrowRightIcon = 'assets/red_arrow_right.svg';
  String liveCodeErrorTitle = '';
  String carbonTitle = '';
  String carbonColor = '';
  bool isSerial = false;
  bool isCarbon = false;
  Color switchRightColor = Color(0x3C3C434D);
  int _pageIndex = 1;
  int get pageIndex => _pageIndex;
  set pageIndex(int currentPageIndex) {
    _pageIndex = currentPageIndex;
  }

  var previewPicture = [];
  // 定义设备标题文本，默认为空
  String deviceTitle = '';
  // 定义连接标题文本，默认为空
  String connectTitle = '';

  int isWifi = 0;
  // 定义RFID数据源异常提示
  String rfidSoureUnCompliance = '';
  // 定义RFID数据源未绑定提示
  String rfidSoureUnBind = '';
  bool isConnectDevice = false;

  bool isOffsetConnect = false;
  // 定义设备消息标题文本，默认为空
  String deviceMsgTitle = '';
  // 定义偏移标题文本，默认为空
  String offsetTitle = '';
  // 定义尺寸吐司标题文本，默认为空
  String sizeToastTitle = '';
  // 定义偏移的文本样式，包括颜色、字体大小和字体权重
  TextStyle? offsetStyle = TextStyle(color: Color(0x993C3C43), fontSize: 13.0, fontWeight: FontWeight.w400);
  // 定义设备标题的文本样式，包括颜色、字体大小和字体权重
  TextStyle? deviceTitleStyle = TextStyle(color: Color(0x993C3C43), fontSize: 15.0, fontWeight: FontWeight.w400);
  // 定义设备消息的文本样式，包括颜色、字体大小和字体权重
  TextStyle? deviceMsgStyle = TextStyle(color: Color(0xFF595959), fontSize: 11.0, fontWeight: FontWeight.w400);
  // 定义尺寸吐司的文本样式，包括颜色、字体大小和字体权重
  TextStyle? sizeToastStyle = TextStyle(color: Color(0xFFFF9900), fontSize: 12.0, fontWeight: FontWeight.w400);
  // 定义设备的背景颜色，默认为白色
  Color deviceBgColor = Colors.white;
  // 定义设备展示的背景颜色，默认为白色
  Color deviceShowColor = Colors.white;

  Color connectDialogColor = Color(0xFFFB4B42);
  // 定义蓝牙图标的路径
  String blueToothIcon = 'assets/blue_tooth.svg';
  // 定义连接蓝牙图标的路径
  String connectBlueToothIcon = 'assets/connect_blue_tooth.svg';
  // 定义尺寸吐司图标的路径
  String sizeToastIcon = 'assets/size_toast.svg';
  // 定义蓝牙箭头图标的路径
  String blueToothArrow = 'assets/left_arrow.svg';
  // 定义设备消息图标的路径
  String deviceMsgIcon = "assets/down_arrow.svg";
  // 定义偏移图标的路径
  String offsetIcon = "assets/offset.svg";

  // 定义打印模型标题文本，默认为空
  String printModelTitle = '';
  // 定义模型名称标题文本，默认为空
  String modelNameTitle = '';
  // 定义打印密度标题文本，默认为空
  String printDensityTitle = '';
  // 默认文本值
  String defaultTextValue = '';
  // 定义模型选择图标的路径
  String modelSelectIcon = "assets/model_select.svg";
  // 定义页面最大值，用于限制可打印范围
  int pageMax = 1;
  // 定义溶解度设置的起始值
  double solubilitySetStart = 1;
  // 定义溶解度设置的结束值
  double solubilitySetEnd = 3;
  // 定义是否显示范围，默认为否
  bool isShowRange = false;

  bool isDataSource = false;
  // 定义是否显示打印优先级，默认为否
  bool isShowPrintPriority = false;
  // 定义打印模型的文本样式，包括颜色、字体大小和字体权重
  TextStyle? printModelStyle = TextStyle(color: Color(0xFF000000), fontSize: 15.0, fontWeight: FontWeight.w400);
  // 定义模型的文本样式，包括颜色、字体大小和字体权重
  TextStyle? modelStyle = TextStyle(color: Color(0x993C3C43), fontSize: 15.0, fontWeight: FontWeight.w400);
  // 定义滑块的背景颜色
  Color sliderBgColor = Color(0xFFF5F5F5);
  // 定义刻度标记的颜色
  Color tickMarkColor = Color(0xFFD9D9D9);
  // 定义滑块主题颜色
  Color sliderThemeColor = Color(0xFFF5F5F5);
  // 定义保存按钮的背景颜色
  Color saveBtnBgColor = Color(0xFFFB4B42);
  // 定义滑块激活颜色
  Color sliderActiveColor = Color(0xFFF5F5F5);
  // 定义滑块标题颜色
  Color sliderTitle = Color(0xFF999999);

  // 定义打印和保存按钮标题文本，默认为空
  String printAndSaveBtnTitle = '';
  // 定义打印按钮标题文本，默认为空
  String printBtnTitle = '';
  // 定义仅打印一个按钮的标题文本，默认为空
  String printJustOneBtnTitle = '';
  // 定义打印和保存按钮的文本样式，包括颜色、字体大小和字体权重
  TextStyle? printAndSaveBtnStyle = TextStyle(color: Color(0xFFFB4B42), fontSize: 15.0, fontWeight: FontWeight.w400);
  // 定义打印按钮的文本样式，包括颜色、字体大小和字体权重
  TextStyle? printBtnStyle = TextStyle(color: Color(0xFFFFFFFF), fontSize: 15.0, fontWeight: FontWeight.w400);
  // 定义仅打印一个按钮的文本样式，包括颜色、字体大小和字体权重
  TextStyle? printJustOneBtnStyle = TextStyle(color: Color(0xFFFB4B42), fontSize: 15.0, fontWeight: FontWeight.w400);
  // 定义打印和保存按钮的背景颜色
  Color printAndSaveBtnBgColor = Color(0x0DFF3B30);
  // 定义打印按钮的背景颜色
  Color printBtnBgColor = Color(0xFFFB4B42);
  // 定义打印按钮的背景颜色
  Color printSettingThemeColor = Color(0xFFFB4B42);
  // 定义打印中按钮的背景颜色
  Color PrintingBtnBgColor = Color(0x0DFF3B30).withAlpha(30);
  // 定义是否下载成功，默认为否
  bool isDownloadSuccess = false;
  // 定义是否正在打印，默认为否
  bool isPrinting = false;
  bool isSavePrinting = false;
  // 定义RFID数据源异常提示文本，默认为空
  String RFIDErrorTitle = '';
}

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';

class PrintBottomBtnWidget extends StatefulWidget {
  PrintSettingLogic logic;

  PrintBottomBtnWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintBottomBtnWidgetState createState() => _PrintBottomBtnWidgetState(logic);
}

class _PrintBottomBtnWidgetState extends State<PrintBottomBtnWidget> {
  PrintSettingLogic logic;
  static double bottomHeight = 0;

  _PrintBottomBtnWidgetState(this.logic);

  @override
  void initState() {
    super.initState();
    logic.stream.listen((event) {
      if (event["action"] == "refreshTemplate") {
        //   logic.isMultipleTemplates();
        if (logic.style.isShowRange) {
          logic.channel.trackEvent("show", "024_067_095");
        }
      }
    });
    WidgetsBinding.instance.endOfFrame.then(
      (value) {
        if (mounted) {
          bottomHeight = MediaQuery.of(context).padding.bottom;
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.printButton,
        builder: (logic) {
          return Container(
            padding: EdgeInsetsDirectional.only(bottom: bottomHeight),
            margin: EdgeInsets.only(top: MediaQuery.of(context).viewInsets.bottom / 1.2),
            color: Colors.white,
            child: Column(
              children: [
                Divider(
                  height: 1,
                  thickness: 0.5,
                  color: Color(0xFFEBEBEB),
                ),
                SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          logic.toPrint();
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44,
                          decoration: BoxDecoration(
                            color: logic.style.printBtnBgColor,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          margin: EdgeInsetsDirectional.symmetric(horizontal: 16),
                          child: logic.style.isPrinting
                              ? Container(
                                  alignment: Alignment.center,
                                  child: const CupertinoActivityIndicator(
                                    radius: 13,
                                    color: Colors.white,
                                    animating: true,
                                  ),
                                )
                              : Text(
                                  logic.style.printBtnTitle,
                                  style: logic.style.printBtnStyle,
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                logic.style.isShowRange && logic.taskType != PrintTaskType.miniApp
                    ? GestureDetector(
                        onTap: () {
                          logic.toPrint(isJustOne: true);
                        },
                        child: Container(
                          child: Text(
                            logic.style.printJustOneBtnTitle,
                            style: logic.style.printJustOneBtnStyle,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    : Container(),
                Container(
                  height: bottomHeight > 0 ? 0 : 20,
                )
              ],
            ),
          );
        });
  }
}

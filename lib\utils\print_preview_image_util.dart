import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/painting.dart';
import 'package:image/image.dart' as img;
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/niimbot_template.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

class PrintPreviewImageUtil {
  static Future<Uint8List> generatePreviewImage(TemplateData templateData,
      {int page = 1,
      Color? firstRfidColor,
      Color? secondRfidColor,
      bool isSupportGray16 = false,
      double? correctRatio,
      bool isShowBindSlash = true}) async {
    Color? contentColor;
    if (firstRfidColor != null && secondRfidColor == null) {
      //单色标签纸或单色碳带
      contentColor = firstRfidColor;
    }
    int index = templateData.multipleBackIndex;
    index = index < templateData.localBackground.length ? index : 0;

    String backgroundUrl = "";
    List<String> backgroundList = templateData.backgroundImage.split(",");
    if (backgroundList.isNotEmpty && index >= 0 && index < backgroundList.length) {
      backgroundUrl = backgroundList[index];
    }
    String backgroundLocal = "";
    if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
      backgroundLocal = templateData.localBackground[index];
    }
    backgroundLocal = backgroundLocal.trim();
    int canvasRotate = (templateData.canvasRotate % 360).toInt();
    if (backgroundLocal.isNotEmpty && canvasRotate != 0) {
      String directory = path.dirname(backgroundLocal);
      String fileName = "/background_${templateData.profile.extra.labelId}_$canvasRotate.png";
      if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
        fileName = "background_${templateData.profile.extra.labelId}_${index}_$canvasRotate.png";
      }
      String rotateBackgroundLocal = directory + path.separator + fileName;
      await rotateImageFile(backgroundLocal, rotateBackgroundLocal, canvasRotate);
      backgroundLocal = rotateBackgroundLocal;
    }
    templateData = resetImageProcessType(templateData, isSupportGray16);
    if (secondRfidColor == null) {
      NetalImageResult result = await TemplateGenerate.generatePreviewImageAsync(templateData,
          page: page,
          color: contentColor,
          backgroundImage: backgroundUrl,
          localBackgroundImageUrl: backgroundLocal,
          isShowBindSlash: isShowBindSlash);
      return result.pixels;
    }
    // NetalImageResult res = await TemplateGenerate.generatePreviewImageAsync(templateData,
    //     page: page, color: contentColor, backgroundImage: "", localBackgroundImageUrl: "");
    // Uint8List contentPreviewData = res.pixels;
    // int width = res.width;
    // int height = res.height;
    // bool transparentWhite = backgroundLocal.isNotEmpty;
    // int templateRotate = (templateData.rotate % 360).toInt();
    // Uint8List? data = handleContentPreviewWithRibbonDoubleColor(
    //     contentPreviewData, width, height, firstRfidColor!, secondRfidColor, transparentWhite, templateRotate);
    // if (data == null) {
    //   NetalImageResult result = await TemplateGenerate.generatePreviewImageAsync(templateData,
    //       page: page, color: contentColor, backgroundImage: backgroundUrl, localBackgroundImageUrl: backgroundLocal);
    //   return result.pixels;
    // }
    // if (backgroundLocal.isNotEmpty) {
    //   Uint8List? previewData = await mixContentPreviewWithBackground(data, backgroundLocal, width, height);
    //   if (previewData != null) {
    //     return previewData;
    //   } else {
    //     NetalImageResult result = await TemplateGenerate.generatePreviewImageAsync(templateData,
    //         page: page, color: contentColor, backgroundImage: backgroundUrl, localBackgroundImageUrl: backgroundLocal);
    //     return result.pixels;
    //   }
    // }
    // return data;
    int templateRotate = (templateData.rotate % 360).toInt();
    int position = templateRotate == 90 || templateRotate == 270 ? 1 : 0;
    Color color1 = firstRfidColor!;
    Color color2 = secondRfidColor;
    if (templateRotate == 180 || templateRotate == 90) {
      color1 = secondRfidColor;
      color2 = firstRfidColor;
    }
    NetalImageResult result = await TemplateGenerate.generateBlackRedPreviewImageAsync(templateData, color1, color2,
        position: position,
        page: page,
        backgroundImage: backgroundUrl,
        localBackgroundImageUrl: backgroundLocal,
        correctRatio: correctRatio,
        isShowBindSlash: isShowBindSlash);
    return result.pixels;
  }

  static Future<Uint8List> generateContentPreviewImage(TemplateData templateData,
      {int page = 1, Color? firstRfidColor, Color? secondRfidColor, bool isSupportGray16 = false}) async {
    Color? contentColor;
    if (firstRfidColor != null && secondRfidColor == null) {
      //单色标签纸或单色碳带
      contentColor = firstRfidColor;
    }
    int index = templateData.multipleBackIndex;
    index = index < templateData.localBackground.length ? index : 0;

    String backgroundUrl = "";
    List<String> backgroundList = templateData.backgroundImage.split(",");
    if (backgroundList.isNotEmpty && index >= 0 && index < backgroundList.length) {
      backgroundUrl = backgroundList[index];
    }
    String backgroundLocal = "";
    if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
      backgroundLocal = templateData.localBackground[index];
    }
    backgroundLocal = backgroundLocal.trim();
    int canvasRotate = (templateData.canvasRotate % 360).toInt();
    if (backgroundLocal.isNotEmpty && canvasRotate != 0) {
      String directory = path.dirname(backgroundLocal);
      String fileName = "/background_${templateData.profile.extra.labelId}_$canvasRotate.png";
      if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
        fileName = "background_${templateData.profile.extra.labelId}_${index}_$canvasRotate.png";
      }
      String rotateBackgroundLocal = directory + path.separator + fileName;
      await rotateImageFile(backgroundLocal, rotateBackgroundLocal, canvasRotate);
      backgroundLocal = rotateBackgroundLocal;
    }
    var transparentPath = await generateTransparentImage();
    backgroundUrl = "";

    templateData = resetImageProcessType(templateData, isSupportGray16);
    if (secondRfidColor == null) {
      // log("==============mergedTemplate116==>templateData: ${templateData.toJson()}, page: $page, ");
      NetalImageResult result = await TemplateGenerate.generatePreviewImageAsync(templateData,
          page: page,
          color: contentColor,
          backgroundImage: backgroundUrl,
          localBackgroundImageUrl: transparentPath,
          isShowBindSlash: true);
      // await saveImageResult(result, 'saved_image');
      return result.pixels;
    }
    int templateRotate = (templateData.rotate % 360).toInt();
    int position = templateRotate == 90 || templateRotate == 270 ? 1 : 0;
    Color color1 = firstRfidColor!;
    Color color2 = secondRfidColor;
    if (templateRotate == 180 || templateRotate == 90) {
      color1 = secondRfidColor;
      color2 = firstRfidColor;
    }
    NetalImageResult result = await TemplateGenerate.generateBlackRedPreviewImageAsync(templateData, color1, color2,
        position: position,
        page: page,
        backgroundImage: backgroundUrl,
        localBackgroundImageUrl: transparentPath,
        isShowBindSlash: true);
    return result.pixels;
  }

  static Future<Uint8List> generateBackgroundPreviewImage(TemplateData templateData,
      {int page = 1, Color? firstRfidColor, Color? secondRfidColor}) async {
    Color? contentColor;
    if (firstRfidColor != null && secondRfidColor == null) {
      //单色标签纸或单色碳带
      contentColor = firstRfidColor;
    }
    int index = templateData.multipleBackIndex;
    index = index < templateData.localBackground.length ? index : 0;

    String backgroundUrl = "";
    List<String> backgroundList = templateData.backgroundImage.split(",");
    if (backgroundList.isNotEmpty && index >= 0 && index < backgroundList.length) {
      backgroundUrl = backgroundList[index];
    }
    String backgroundLocal = "";
    if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
      backgroundLocal = templateData.localBackground[index];
    }
    backgroundLocal = backgroundLocal.trim();
    int canvasRotate = (templateData.canvasRotate % 360).toInt();
    if (backgroundLocal.isNotEmpty && canvasRotate != 0) {
      String directory = path.dirname(backgroundLocal);
      String fileName = "background_${templateData.profile.extra.labelId}_$canvasRotate.png";
      if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
        fileName = "background_${templateData.profile.extra.labelId}_${index}_$canvasRotate.png";
      }
      String rotateBackgroundLocal = directory + path.separator + fileName;
      await rotateImageFile(backgroundLocal, rotateBackgroundLocal, canvasRotate);
      backgroundLocal = rotateBackgroundLocal;
    }
    if (secondRfidColor == null) {
      // log("==============mergedTemplate117==>templateData: ${templateData.toJson()}, page: $page, ");
      NetalImageResult result = await TemplateGenerate.generatePreviewImageAsync(
          templateData.copyWith(elements: [], dataSources: CopyWrapper.value([])),
          page: page,
          color: contentColor,
          backgroundImage: backgroundUrl,
          localBackgroundImageUrl: backgroundLocal);
      // await saveImageResult(result, 'saved_image_bg');
      return result.pixels;
    }
    int templateRotate = (templateData.rotate % 360).toInt();
    int position = templateRotate == 90 || templateRotate == 270 ? 1 : 0;
    Color color1 = firstRfidColor!;
    Color color2 = secondRfidColor;
    if (templateRotate == 180 || templateRotate == 90) {
      color1 = secondRfidColor;
      color2 = firstRfidColor;
    }
    NetalImageResult result = await TemplateGenerate.generateBlackRedPreviewImageAsync(
        templateData.copyWith(elements: [], dataSources: CopyWrapper.value([])), color1, color2,
        position: position, page: page, backgroundImage: backgroundUrl, localBackgroundImageUrl: backgroundLocal);
    return result.pixels;
  }

  static Future rotateImageFile(String imageSrc, String imageDes, num angle) async {
    File imageDesFile = File(imageDes);
    if (imageDesFile.existsSync()) {
      return null;
    } else {
      final cmd = img.Command()
        ..decodeImageFile(imageSrc)
        ..copyRotate(angle: angle)
        ..writeToFile(imageDes);
      return await cmd.executeThread();
    }
  }

  static Uint8List? handleContentPreviewWithRibbonDoubleColor(Uint8List dataSource, int width, int height,
      Color ribbonFirstColor, Color ribbonSecondColor, bool transparentWhite, int templateRotate) {
    img.Image? image = img.decodeImage(dataSource);
    if (image == null) {
      return null;
    }
    final handleFirst = !isBlackColor(ribbonFirstColor); // Color.BLACK
    final handleSecond = !isBlackColor(ribbonSecondColor); // Color.BLACK
    final rotate = (templateRotate % 360).toInt();
    for (int i = 0; i < height; i++) {
      for (int j = 0; j < width; j++) {
        img.Pixel pixel = image.getPixel(j, i);
        if (pixel.r == 0 && pixel.g == 0 && pixel.b == 0 && pixel.a == 0) {
          // Color.TRANSPARENT
          continue;
        }

        void handleFirstFun() {
          if (pixel.r == 255 && pixel.g == 255 && pixel.b == 255 && pixel.a == 255) {
            // Color.WHITE
            if (transparentWhite) {
              // Color.TRANSPARENT
              image.setPixelRgba(j, i, 0, 0, 0, 0);
            }
          } else {
            if (handleFirst) {
              image.setPixelRgba(
                  j, i, ribbonFirstColor.red, ribbonFirstColor.green, ribbonFirstColor.blue, ribbonFirstColor.alpha);
            }
          }
        }

        void handleSecondFun() {
          if (pixel.r == 255 && pixel.g == 255 && pixel.b == 255 && pixel.a == 255) {
            // Color.WHITE
            if (transparentWhite) {
              // Color.TRANSPARENT
              image.setPixelRgba(j, i, 0, 0, 0, 0);
            }
          } else {
            if (handleSecond) {
              image.setPixelRgba(j, i, ribbonSecondColor.red, ribbonSecondColor.green, ribbonSecondColor.blue,
                  ribbonSecondColor.alpha);
            }
          }
        }

        if (rotate == 90) {
          if (i < (0.5 * height).toInt()) {
            handleSecondFun();
          } else {
            handleFirstFun();
          }
        } else if (rotate == 180) {
          if (j < (0.5 * width).toInt()) {
            handleSecondFun();
          } else {
            handleFirstFun();
          }
        } else if (rotate == 270) {
          if (i >= (0.5 * height).toInt()) {
            handleSecondFun();
          } else {
            handleFirstFun();
          }
        } else {
          if (j >= (0.5 * width).toInt()) {
            handleSecondFun();
          } else {
            handleFirstFun();
          }
        }
      }
    }
    return img.encodePng(image);
  }

  static Future<Uint8List?> mixContentPreviewWithBackground(
      Uint8List contentPreviewData, String backgroundPath, int width, int height) async {
    Image backgroundImage = await resizeImage(backgroundPath, width, height);
    // Codec codes = await instantiateImageCodec(contentPreviewData);
    // FrameInfo frameInfo = await codes.getNextFrame();
    // Image contentPreviewImage = frameInfo.image;
    Image contentPreviewImage = await decodeImageFromList(contentPreviewData);
    Paint paint = Paint();
    PictureRecorder recorder = PictureRecorder();
    Canvas canvas = Canvas(recorder);
    canvas.drawImage(backgroundImage, const Offset(0, 0), paint);
    canvas.drawImage(contentPreviewImage, const Offset(0, 0), paint);
    Picture picture = recorder.endRecording();
    Image mixImage = await picture.toImage(width, height);
    ByteData? data = await mixImage.toByteData(format: ImageByteFormat.png);
    if (data == null) {
      return null;
    }
    return data.buffer.asUint8List();
  }

  static Future<Image> resizeImage(String filePath, int width, int height) async {
    Uint8List imageData = await File(filePath).readAsBytes();
    Image image = await decodeImageFromList(imageData);
    if (image.width == width && image.height == height) {
      return image;
    }
    double scaleX = width.toDouble() / image.width;
    double scaleY = height.toDouble() / image.height;
    PictureRecorder recorder = PictureRecorder();
    Canvas canvas = Canvas(recorder, Rect.fromPoints(const Offset(0, 0), Offset(width.toDouble(), height.toDouble())));
    canvas.scale(scaleX, scaleY);
    canvas.drawImage(image, const Offset(0, 0), Paint());
    Picture picture = recorder.endRecording();
    return await picture.toImage(width, height);
  }

  ///生成透明背景图
  static Future<String> generateTransparentImage() async {
    final recorder = PictureRecorder();
    final canvas = Canvas(recorder);
    final size = Size(50, 50); // 图片的宽度和高度

    // 绘制透明背景
    canvas.drawColor(Color(0x00000000), BlendMode.src);
    final picture = recorder.endRecording();
    final image = await picture.toImage(size.width.toInt(), size.height.toInt());
    final byteData = await image.toByteData(format: ImageByteFormat.png);
    final pngBytes = byteData?.buffer.asUint8List();

    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/transparent_image.png');
    await file.writeAsBytes(pngBytes!);
    return file.path;
  }

  static bool isBlackColor(Color color) {
    return color.red == 0 && color.green == 0 && color.blue == 0 && color.alpha == 255;
  }

  static Future<String?> saveImageResult(NetalImageResult result, String imgName) async {
    try {
      // 1️⃣ 获取应用文档目录路径
      final directory = await getApplicationDocumentsDirectory();
      final String filePath = '${directory.path}/$imgName.png';

      // 2️⃣ 写入 Uint8List 数据到文件
      File file = File(filePath);
      await file.writeAsBytes(result.pixels);

      print('✅ 图片保存成功: $filePath');
      return filePath;
    } catch (e) {
      print('❌ 保存图片失败: $e');
      return null;
    }
  }

  static TemplateData resetImageProcessType(TemplateData templateData, bool isSupportGray16) {
    List<BaseElement> baseElements = [];
    for (var element in templateData.elements) {
      if (element is ImageElement) {
        ImageElement imageElement = element.copyWith();
        if (element.imageProcessingType == NetalImageRenderType.gradient && !isSupportGray16) {
          imageElement =
              imageElement.copyWith(imageProcessingType: NetalImageRenderType.grayscale, imageProcessingValue: [5]);
        }
        baseElements.add(imageElement);
      } else {
        baseElements.add(element);
      }
    }
    return templateData.copyWith(elements: baseElements);
  }
}

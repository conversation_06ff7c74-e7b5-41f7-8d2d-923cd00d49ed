import 'dart:io';

import 'package:flutter/material.dart';

class PrintContainPage extends StatefulWidget {
  Widget child;
  PrintContainPage({Key? key, required this.child}) : super(key: key);

  @override
  _PrintContainPageState createState() => _PrintContainPageState();
}

class _PrintContainPageState extends State<PrintContainPage> {
  @override
  void initState() {
    super.initState();
    // 在当前上下文中添加一个模态底部导航栏，该导航栏在屏幕下方滑入，用于显示临时内容
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 显示一个带有自定义样式的模态底部导航栏，该导航栏无法通过拖动来关闭
      showModalBottomSheet(
        context: context,
        isScrollControlled: true, // 允许导航栏内部的内容滚动
        isDismissible: false, // 点击外部或按下返回按钮时不会关闭导航栏
        enableDrag: false, // 导航栏不能通过拖动来关闭
        shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))), // 导航栏的上边缘有圆角
        builder: (BuildContext context) {
          // 返回要在导航栏中显示的子组件
          return widget.child;
        },
      ).then((value) {
        // 在导航栏关闭后，关闭当前的Navigator页面
        if(Platform.isAndroid) {
          Navigator.pop(context);
        }
        else {
          Future.delayed(Duration(milliseconds: 200), () {
            Navigator.pop(context);
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
    );
  }
}

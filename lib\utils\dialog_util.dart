import 'package:flutter/material.dart';

import '../interface/print_setting_channel_interface.dart';

class DialogUtil {
  // void showCustomDialog(BuildContext context, String title, String content,
  //     {bool justSureButton = false,
  //     bool dismissOutSideTouch = true,
  //     int maxLine = -1,
  //     String leftFunStr = '取消',
  //     String rightFunStr = '确定',
  //     VoidCallback? leftFunCall,
  //     VoidCallback? rightFunCall,
  //     TextStyle? titleTextStyle,
  //     TextStyle? contentTextStyle,
  //     Color? rightTextColor,
  //     bool willPopWrapper = false}) async {
  //   Color? rightColor = rightTextColor ?? Colors.white;
  //   Widget contentWidget = Text(
  //     "$content",
  //     textAlign: TextAlign.center,
  //     style:
  //         contentTextStyle == null ? TextStyle(color: Color.fromRGBO(99, 99, 99, 1.0), fontSize: 15) : contentTextStyle,
  //   );
  //   if (maxLine > 0) {
  //     contentWidget = Text(
  //       "$content",
  //       textAlign: TextAlign.left,
  //       maxLines: maxLine,
  //       style: TextStyle(color: Color(0xFF000000), fontSize: 14),
  //     );
  //   }
  //   showDialog(
  //       useRootNavigator: false,
  //       context: context,
  //       barrierDismissible: dismissOutSideTouch,
  //       builder: (BuildContext context) {
  //         SimpleDialog simpleDialog = SimpleDialog(
  //           titlePadding: EdgeInsetsDirectional.fromSTEB(24.0, 24.0, 24.0, 0.0),
  //           shape: RoundedRectangleBorder(
  //             borderRadius: BorderRadius.circular(12),
  //           ),
  //           backgroundColor: Colors.white,
  //           elevation: 0,
  //           contentPadding: EdgeInsets.fromLTRB(0, 6, 0, 0),
  //           title: title.isEmpty
  //               ? null
  //               : Container(
  //                   alignment: Alignment.center,
  //                   child: Text(
  //                     "$title",
  //                     textAlign: TextAlign.center,
  //                     style: titleTextStyle == null
  //                         ? TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 16, fontWeight: FontWeight.w600)
  //                         : titleTextStyle,
  //                   ),
  //                 ),
  //           children: <Widget>[
  //             Container(
  //               padding: EdgeInsetsDirectional.fromSTEB(20, title.isEmpty ? 14 : 0, 20, 19),
  //               child: contentWidget,
  //             ),
  //             Container(
  //               child: Row(
  //                 children: <Widget>[
  //                   justSureButton
  //                       ? Container()
  //                       : Expanded(
  //                           flex: 1,
  //                           child: InkWell(
  //                             highlightColor: Colors.transparent,
  //                             splashColor: Colors.transparent,
  //                             onTap: () {
  //                               Navigator.of(context).pop(true);
  //                               if (leftFunCall != null) {
  //                                 leftFunCall();
  //                               }
  //                             },
  //                             child: Container(
  //                               margin: EdgeInsetsDirectional.all(12),
  //                               alignment: Alignment.center,
  //                               decoration: BoxDecoration(
  //                                 color: Color(0xFFF7F7FA), // 设置背景色
  //                                 borderRadius: BorderRadius.all(Radius.circular(10)),
  //                               ),
  //                               height: 44,
  //                               child: Text(
  //                                 leftFunStr,
  //                                 textAlign: TextAlign.center,
  //                                 style: TextStyle(color: Color(0xFF595959), fontSize: 17, fontWeight: FontWeight.w400),
  //                               ),
  //                             ),
  //                           ),
  //                         ),
  //                   Expanded(
  //                     flex: 1,
  //                     child: InkWell(
  //                       highlightColor: Colors.transparent,
  //                       splashColor: Colors.transparent,
  //                       onTap: () {
  //                         Navigator.of(context).pop(true);
  //                         if (rightFunCall != null) {
  //                           rightFunCall();
  //                         }
  //                       },
  //                       child: Container(
  //                         margin: EdgeInsetsDirectional.all(12),
  //                         alignment: Alignment.center,
  //                         decoration: BoxDecoration(
  //                           color: Color(0xFFFB4B42), // 设置背景色
  //                           borderRadius: BorderRadius.all(Radius.circular(10)),
  //                         ),
  //                         height: 44,
  //                         child: Text(
  //                           rightFunStr,
  //                           textAlign: TextAlign.center,
  //                           style: TextStyle(color: rightColor, fontSize: 17, fontWeight: FontWeight.w400),
  //                         ),
  //                       ),
  //                     ),
  //                   )
  //                 ],
  //               ),
  //             )
  //           ],
  //         );
  //         return MediaQuery(
  //           child: willPopWrapper ? WillPopScope(child: simpleDialog, onWillPop: () async => false) : simpleDialog,
  //           data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0, boldText: false),
  //         );
  //       });
  // }

  void showCustomDialog(BuildContext context, String title, String content,
      {bool justSureButton = false,
      bool dismissOutSideTouch = false,
      int maxLine = -1,
      String leftFunStr = '取消',
      String rightFunStr = '确定',
      VoidCallback? leftFunCall,
      VoidCallback? rightFunCall,
      TextStyle? titleTextStyle,
      TextStyle? contentTextStyle,
      Color? rightTextColor,
      Color? rightBackgroundColor = const Color(0xFFFB4B42),
      bool withButtonOutline = false,
      bool willPopWrapper = false,
      PrintSettingChannelInterface? channel}) async {

    Color? rightColor = rightTextColor ?? Colors.white;
    Widget contentWidget = Text(
      content,
      textAlign: TextAlign.center,
      style: contentTextStyle ?? TextStyle(color: Color.fromRGBO(99, 99, 99, 1.0), fontSize: 15),
    );
    if (maxLine > 0) {
      contentWidget = Text(
        content,
        textAlign: TextAlign.left,
        maxLines: maxLine,
        style: const TextStyle(color: Color(0xFF000000), fontSize: 14),
      );
    }
    channel?.setFlutterVCCanSideslip(false);
    showDialog(
        useRootNavigator: false,
        context: context,
        barrierColor: const Color.fromRGBO(0, 0, 0, 0.35),
        barrierDismissible: dismissOutSideTouch,
        builder: (BuildContext context) {
          SimpleDialog simpleDialog = SimpleDialog(
            titlePadding: const EdgeInsetsDirectional.fromSTEB(24.0, 24.0, 24.0, 0.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            elevation: 0,
            contentPadding: const EdgeInsets.fromLTRB(0, 6, 0, 0),
            title: title.isEmpty
                ? null
                : Container(
                    alignment: Alignment.center,
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: titleTextStyle ??
                          const TextStyle(
                              color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
            children: <Widget>[
              Container(
                padding: EdgeInsetsDirectional.fromSTEB(20, title.isEmpty ? 14 : 0, 20, 19),
                decoration: BoxDecoration(
                    border: withButtonOutline
                        ? const Border()
                        : Border(
                            bottom: Divider.createBorderSide(context, width: 0.6),
                          )),
                child: contentWidget,
              ),
              withButtonOutline
                  ? Row(
                      children: <Widget>[
                        justSureButton
                            ? Container()
                            : Expanded(
                                flex: 1,
                                child: InkWell(
                                  highlightColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  onTap: () {
                                    Navigator.of(context).pop(true);
                                    if (leftFunCall != null) {
                                      leftFunCall();
                                    }
                                  },
                                  child: Container(
                                    margin: const EdgeInsetsDirectional.all(12),
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(
                                      color: Color(0xFFF7F7FA), // 设置背景色
                                      borderRadius: BorderRadius.all(Radius.circular(10)),
                                    ),
                                    height: 44,
                                    child: Text(
                                      leftFunStr,
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                          color: Color(0xFF595959), fontSize: 17, fontWeight: FontWeight.w400),
                                    ),
                                  ),
                                ),
                              ),
                        Expanded(
                          flex: 1,
                          child: InkWell(
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onTap: () {
                              Navigator.of(context).pop(true);
                              if (rightFunCall != null) {
                                rightFunCall();
                              }
                            },
                            child: Container(
                              margin: const EdgeInsetsDirectional.all(12),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: rightBackgroundColor, // 设置背景色
                                borderRadius: const BorderRadius.all(Radius.circular(10)),
                              ),
                              height: 44,
                              child: Text(
                                rightFunStr,
                                textAlign: TextAlign.center,
                                style: TextStyle(color: rightColor, fontSize: 17, fontWeight: FontWeight.w400),
                              ),
                            ),
                          ),
                        )
                      ],
                    )
                  : Row(
                      children: <Widget>[
                        justSureButton
                            ? Container()
                            : Expanded(
                                flex: 1,
                                child: Material(
                                  borderRadius: const BorderRadiusDirectional.only(bottomStart: Radius.circular(12)),
                                  color: const Color(0xFFFFFFFF),
                                  child: InkWell(
                                    onTap: () {
                                      Navigator.of(context).pop(true);
                                      if (leftFunCall != null) {
                                        leftFunCall();
                                      }
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: 50,
                                      child: Text(
                                        leftFunStr,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                            color: Color(0xFF595959), fontSize: 17, fontWeight: FontWeight.w400),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                        Expanded(
                          flex: 1,
                          child: Material(
                            borderRadius: BorderRadiusDirectional.only(
                                bottomEnd: const Radius.circular(12),
                                bottomStart: Radius.circular(justSureButton ? 12 : 0)),
                            color: const Color(0xFFFFFFFF),
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop(true);
                                if (rightFunCall != null) {
                                  rightFunCall();
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    border: BorderDirectional(start: Divider.createBorderSide(context, width: 0.5))),
                                alignment: Alignment.center,
                                height: 50,
                                child: Text(
                                  rightFunStr,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: justSureButton ? const Color(0xFF5C88C1) : const Color(0xFFFB4B42),
                                      fontSize: 17,
                                      fontWeight: FontWeight.w400),
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    )
            ],
          );
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(boldText: false, textScaler: const TextScaler.linear(1.0)),
            child: willPopWrapper ? WillPopScope(child: simpleDialog, onWillPop: () async => false) : simpleDialog,
          );
        }).then((value) {
      channel?.setFlutterVCCanSideslip(true);
    });
  }
}

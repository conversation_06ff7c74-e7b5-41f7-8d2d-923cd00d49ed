import 'package:flutter/material.dart';

class ItemDivider extends StatelessWidget {
  final EdgeInsets? margin;
  final double height;

  const ItemDivider({super.key, this.margin, this.height = 0.5});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: margin ?? EdgeInsets.zero,
      color: Colors.white,
      child: Container(
        height: height,
        width: double.infinity,
        color: const Color(0xFFE4E5E6),
      ),
    );
  }
}

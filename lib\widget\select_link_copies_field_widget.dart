import 'dart:math';

import 'package:flutter/material.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/color.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';

import 'item_divider.dart';

class SelectLinkCopiesFieldWidget extends StatefulWidget {
  PrintSettingLogic logic;
  List<String> fields;
  int currentIndex ;

  SelectLinkCopiesFieldWidget({super.key, required this.fields, required this.logic,required this.currentIndex});

  @override
  State<StatefulWidget> createState() => SelectLinkCopiesFieldState();
}

class SelectLinkCopiesFieldState extends State<SelectLinkCopiesFieldWidget> {
  int currentIndex = -1;
  List<String> realFields = [];

  @override
  void initState() {
    super.initState();
    currentIndex = widget.currentIndex;
    realFields = widget.fields.take(30).toList();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0)),
      child: Container(
          height: MediaQuery.sizeOf(context).height * 0.6,
          color: KColor.COLOR_F5F5F5,
          child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTitleWidget(),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(32, 16, 16, 10),
                  child: Text(widget.logic.getI18nString('app100001153', 'Excel列'),
                      //'Excel列',
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: KColor.COLOR_999999)),
                ),
                Container(
                    height: min(realFields.length * 48, MediaQuery.sizeOf(context).height * 0.4),
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration:
                        const BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(12))),
                    child: ListView.separated(
                      itemCount: realFields.length,
                      itemBuilder: (context, index) {
                        return _buildFieldItem(index);
                      },
                      separatorBuilder: (context, index) {
                        return const ItemDivider(
                          margin: EdgeInsets.only(left: 38),
                        );
                      },
                    )),
              ])),
    );
  }

  Widget _buildFieldItem(int index) {
    String field = realFields[index];

    return GestureDetector(
      onTap: () {
        if (currentIndex != index) {
          currentIndex = index;
          setState(() {});
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              Opacity(
                opacity: index == currentIndex ? 1 : 0,
                child: Container(
                  margin: const EdgeInsets.only(left: 10, right: 8),
                  child: const SvgIcon(
                    'assets/icon_checked_gou.svg',
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
              Expanded(
                child:
                    Text(field,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black)),
              )
            ],
          )),
    );
  }

  Widget _buildTitleWidget() {
    return Container(
        color: Colors.white,
        height: 48,
        child: Row(
          children: [
            Container(
              padding: Directionality.of(context) == TextDirection.rtl
                  ? const EdgeInsets.only(right: 16)
                  : const EdgeInsets.only(left: 16),
              width: 80,
              child: Align(
                alignment:
                    Directionality.of(context) == TextDirection.rtl ? Alignment.centerRight : Alignment.centerLeft,
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(widget.logic.getI18nString('app100000692', '取消'),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: KColor.COLOR_262626)),
                ),
              ),
            ),
            Expanded(
                child: Center(
              child: Text(widget.logic.getI18nString('app100001938', '关联份数'),
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 17, fontWeight: FontWeight.w600, color: Colors.black)),
            )),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: _confirmImport,
              child: Container(
                padding: Directionality.of(context) == TextDirection.rtl
                    ? const EdgeInsets.only(left: 16)
                    : const EdgeInsets.only(right: 16),
                width: 80,
                child: Align(
                  alignment:
                      Directionality.of(context) == TextDirection.rtl ? Alignment.centerLeft : Alignment.centerRight,
                  child: Text(widget.logic.getI18nString('app00048', '确定'),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: KColor.RED)),
                ),
              ),
            ),
          ],
        ));
  }

  _confirmImport() {
    Navigator.pop(context,currentIndex);
  }
}

import 'package:flutter/material.dart';
import 'package:niimbot_mobile_ui/animations/animations.dart';
import 'package:niimbot_mobile_ui/niimbot_ui.dart';

enum NiimbotButtonType {
  primary,
  secondary,
  alert,
  link,
  pop,
}

class NiimbotPrintButton extends StatefulWidget {
  const NiimbotPrintButton(
      {super.key,

      /// 按钮类型：一级按钮、二级按钮、警示按钮、文字按钮、弹框按钮
      this.type = NiimbotButtonType.primary,

      /// 是否开启紧凑模式
      this.dense = false,

      /// 按钮宽度
      this.width,

      /// 按钮高度
      this.height,

      /// 按钮内容对齐方式
      this.alignment = Alignment.center,

      /// 按钮图标
      this.icon,

      /// 按钮后缀图标
      this.suffixIcon,

      /// 按钮文案
      required this.text,

      /// 是否禁用
      this.enable = true,

      /// 是否正在加载
      this.isLoading = false,

      /// 点击事件
      this.onPressed,

      /// 长按事件
      this.onLongPress,

      /// 悬浮事件
      this.onHover,

      /// 聚焦事件
      this.onFocusChange,

      /// 设置按钮是否自动获取焦点
      this.autoFocus = false,
      this.focusNode,
      this.statesController,
      this.foregroundThemeColor});

  final NiimbotButtonType? type;
  final bool dense;
  final double? width;
  final double? height;
  final Alignment? alignment;
  final Widget? icon;
  final Widget? suffixIcon;
  final String? text;
  final bool enable;
  final bool isLoading;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final ValueChanged<bool>? onHover;
  final ValueChanged<bool>? onFocusChange;
  final bool autoFocus;
  final FocusNode? focusNode;
  final WidgetStatesController? statesController;
  final Color? foregroundThemeColor;
  @override
  State<NiimbotPrintButton> createState() => _NiimbotButtonState();
}

class _NiimbotButtonState extends State<NiimbotPrintButton> with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    final WidgetStateProperty<TextStyle?> defaultTextStyle = WidgetStateProperty.all(const TextStyle(fontSize: 15));
    final WidgetStateProperty<TextStyle?> denseTextStyle = WidgetStateProperty.all(const TextStyle(fontSize: 15));
    final WidgetStateProperty<OutlinedBorder?> defaultShape = WidgetStateProperty.all(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
    );
    final WidgetStateProperty<OutlinedBorder?> denseShape = WidgetStateProperty.all(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
    );

    // primary
    const Color primaryNormalColor = Color.fromRGBO(251, 75, 66, 1);
    const Color primaryHoverColor = Color.fromRGBO(229, 69, 60, 1);
    const Color primaryPressedColor = Color.fromRGBO(209, 62, 55, 1);
    const Color primaryDisabledColor = Color.fromRGBO(251, 75, 66, 0.3);

    // secondary
    const Color secondaryNormalColor = Color.fromRGBO(120, 120, 128, 0.08);
    const Color secondaryHoverColor = Color.fromRGBO(120, 120, 128, 0.16);
    const Color secondaryPressedColor = Color.fromRGBO(120, 120, 128, 0.2);
    const Color secondaryDisabledColor = Color.fromRGBO(120, 120, 128, 0.08);

    // color
    final WidgetStateProperty<Color?> whiteColor = WidgetStateProperty.all(const Color.fromRGBO(255, 255, 255, 1));
    final WidgetStateProperty<Color?> blackColor = WidgetStateProperty.all(const Color.fromRGBO(29, 29, 41, 1));
    final WidgetStateProperty<Color?> disabledColor = WidgetStateProperty.all(const Color.fromRGBO(60, 60, 67, 0.3));
    final WidgetStateProperty<Color?> redColor = WidgetStateProperty.all(const Color.fromRGBO(255, 59, 48, 1));
    final WidgetStateProperty<Color?> alertDisabledColor =
        WidgetStateProperty.all(const Color.fromRGBO(251, 75, 66, 0.3));

    final WidgetStateProperty<EdgeInsetsGeometry?> defaultPadding = WidgetStateProperty.all(
      (const EdgeInsets.symmetric(horizontal: 22)),
    );
    final WidgetStateProperty<EdgeInsetsGeometry?> densePadding = WidgetStateProperty.all(
      (const EdgeInsets.symmetric(horizontal: 8.0)),
    );

    renderTypes() {
      final WidgetStateProperty<Color?> primaryTransitionColor = WidgetStateProperty.resolveWith(
        (states) {
          if (widget.enable) {
            if (states.contains(WidgetState.pressed)) {
              return primaryNormalColor;
            }
            if (states.contains(WidgetState.hovered)) {
              return primaryHoverColor;
            }
            return primaryNormalColor;
          } else {
            return primaryDisabledColor;
          }
        },
      );
      final WidgetStateProperty<Color?> secondaryTransitionColor = WidgetStateProperty.resolveWith(
        (states) {
          if (widget.enable) {
            if (states.contains(WidgetState.pressed)) {
              return secondaryNormalColor;
            }
            if (states.contains(WidgetState.hovered)) {
              return secondaryHoverColor;
            }
            return secondaryNormalColor;
          } else {
            return secondaryDisabledColor;
          }
        },
      );
      final WidgetStateProperty<Color?> popTransitionColor = WidgetStateProperty.resolveWith(
        (states) {
          if (widget.enable) {
            if (states.contains(WidgetState.pressed)) {
              return Colors.transparent;
            }
            if (states.contains(WidgetState.hovered)) {
              return const Color.fromRGBO(120, 120, 128, 0.12);
            }
            return Colors.transparent;
          } else {
            return Colors.transparent;
          }
        },
      );

      if (widget.type == NiimbotButtonType.primary) {
        return ButtonStyle(
          alignment: widget.alignment,
          textStyle: widget.dense ? denseTextStyle : defaultTextStyle,
          padding: widget.dense ? densePadding : defaultPadding,
          shape: widget.dense ? denseShape : defaultShape,
          foregroundColor: whiteColor,
          backgroundColor: primaryTransitionColor,
        );
      } else if (widget.type == NiimbotButtonType.secondary) {
        return ButtonStyle(
          alignment: widget.alignment,
          textStyle: widget.dense ? denseTextStyle : defaultTextStyle,
          padding: widget.dense ? densePadding : defaultPadding,
          shape: widget.dense ? denseShape : defaultShape,
          foregroundColor: widget.enable ? blackColor : disabledColor,
          backgroundColor: secondaryTransitionColor,
          overlayColor: WidgetStateProperty.resolveWith(
            (states) {
              // if (states.contains(WidgetState.hovered)) {
              //   // 悬浮时的颜色，这里设置为透明
              //   return Colors.transparent;
              // }
              // // 默认情况下返回null，表示不设置悬浮时的颜色
              // return null;
              return Colors.transparent;
            },
          ),
        );
      } else if (widget.type == NiimbotButtonType.alert) {
        return ButtonStyle(
          alignment: widget.alignment,
          textStyle: widget.dense ? denseTextStyle : defaultTextStyle,
          padding: widget.dense ? densePadding : defaultPadding,
          shape: widget.dense ? denseShape : defaultShape,
          foregroundColor: widget.enable
              ? (widget.foregroundThemeColor == null
                  ? redColor
                  : WidgetStateProperty.resolveWith(
                      (states) {
                        return widget.foregroundThemeColor!;
                      },
                    ))
              : (widget.foregroundThemeColor == null
                  ? alertDisabledColor
                  : WidgetStateProperty.resolveWith(
                      (states) {
                        return widget.foregroundThemeColor!;
                      },
                    )),
          backgroundColor: secondaryTransitionColor,
          overlayColor: WidgetStateProperty.resolveWith(
            (states) {
              // if (states.contains(WidgetState.hovered)) {
              //   // 悬浮时的颜色，这里设置为透明
              //   return Colors.transparent;
              // }
              // 默认情况下返回null，表示不设置悬浮时的颜色
              return Colors.transparent;
            },
          ),
        );
      } else if (widget.type == NiimbotButtonType.link) {
        return ButtonStyle(
          alignment: widget.alignment,
          textStyle: defaultTextStyle,
          shape: defaultShape,
          foregroundColor: primaryTransitionColor,
          overlayColor: WidgetStateProperty.resolveWith(
            (states) {
              // if (states.contains(WidgetState.hovered)) {
              //   // 悬浮时的颜色，这里设置为透明
              //   return Colors.transparent;
              // }
              // // 默认情况下返回null，表示不设置悬浮时的颜色
              // return null;
              return Colors.transparent;
            },
          ),
        );
      } else if (widget.type == NiimbotButtonType.pop) {
        return ButtonStyle(
          alignment: widget.alignment,
          textStyle: defaultTextStyle,
          shape: defaultShape,
          foregroundColor: widget.enable ? blackColor : disabledColor,
          backgroundColor: popTransitionColor,
          overlayColor: WidgetStateProperty.resolveWith(
            (states) {
              // if (states.contains(WidgetState.hovered)) {
              //   // 悬浮时的颜色，这里设置为透明
              //   return Colors.transparent;
              // }
              // // 默认情况下返回null，表示不设置悬浮时的颜色
              // return null;
              return Colors.transparent;
            },
          ),
        );
      }
    }

    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: TextButton(
          autofocus: widget.autoFocus,
          onPressed: widget.enable && !widget.isLoading ? widget.onPressed : null,
          onLongPress: widget.onLongPress,
          onHover: widget.onHover,
          onFocusChange: widget.onFocusChange,
          focusNode: widget.focusNode,
          statesController: widget.statesController,
          style: renderTypes(),
          child: Wrap(
              alignment: WrapAlignment.center,
              runAlignment: WrapAlignment.center,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                widget.isLoading
                    ? Padding(
                        padding: const EdgeInsets.only(right: 4),
                        child: widget.type == NiimbotButtonType.primary || NiimbotUI().store.mode == ThemeMode.dark
                            ? NiimbotAnimations.loadingWhite(
                                width: 16.0,
                                height: 16.0,
                              )
                            : NiimbotAnimations.loadingBlack(
                                width: 16.0,
                                height: 16.0,
                              ))
                    : const SizedBox(),
                widget.icon != null
                    ? Padding(padding: const EdgeInsets.only(right: 4), child: widget.icon)
                    : const SizedBox(),
                Text('${widget.text}',
                    style: const TextStyle(
                      height: 1.14,
                      fontWeight: FontWeight.w400,
                    ), textAlign: TextAlign.center),
                widget.suffixIcon != null
                    ? Padding(padding: const EdgeInsets.only(left: 4), child: widget.suffixIcon)
                    : const SizedBox(),
              ])),
    );
  }
}

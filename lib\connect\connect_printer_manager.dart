import 'package:nety/models/niimbot_bluetooth_printer.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_wifi_printer.dart';
import 'package:nety/nety.dart';
import 'package:nety/store.dart';
import 'package:niimbot_print_setting_plugin/extensions/change_notifer.dart';

class ConnectPrinterManager{
  //断开连接
  static Function()? disconnectCallback;
  //电量变化
  static Function(int)? electricityCallback;
  //开合盖回调事件
  static Function(int?, int)? coverStatusCallback;
  //是否安装标签纸回调事件：0-已安装，1-未安装
  static Function(int)? paperStatusCallback;
  //是否安装碳带回调事件：0-已安装，1-未安装
  static Function(int)? ribbonStatusCallback;
  //是否读取到rfid标签纸信息：0-未读取到，1-已读取到
  static Function(int?, int)? paperRfidStatusCallback;
  //是否读取到rfid碳带信息：0-未读取到，1-已读取到
  static Function(int?, int)? ribbonRfidStatusCallback;
  //wifi信号回调事件
  static Function(int)? wifiRssiCallback;
  static ConnectPrinterManager? _instance;

  // Avoid self instance
  ConnectPrinterManager._();

  factory ConnectPrinterManager() {
    if(_instance == null) {
      _instance = ConnectPrinterManager._();
      _setPrintSdkCallback();
    }
    return _instance!;
  }

  /// 连接蓝牙打印机
  Future<NiimbotBluetoothPrinter?> connectBluetoothPrinter(NiimbotBluetoothPrinter printer) async{
    NiimbotPrinter? connectedPrinter = await NiimbotPrintSDK().connectPrinter(printer);
    if(connectedPrinter != null){
      return connectedPrinter as NiimbotBluetoothPrinter;
    }
    return null;
  }

  /// 连接wifi打印机
  Future<NiimbotWIFIPrinter?> connectWIFIPrinter(NiimbotWIFIPrinter printer) async{
    NiimbotPrinter? connectedPrinter = await NiimbotPrintSDK().connectPrinter(printer);
    if(connectedPrinter != null){
      return connectedPrinter as NiimbotWIFIPrinter;
    }
    return null;
  }

  /// 打印机断开连接
  Future<bool> disconnectPrinter() async{
    try {
      bool result = await NiimbotPrintSDK().disconnectPrinter();
      return result;
    }
    catch (e) {
      return false;
    }
  }

  /// 获取打印机连接状态 0-未连接。1-ble连接 2-wifi 连接
  Future<int?> isConnectingState() async {
    return await NiimbotPrintSDK().isConnectingState();
  }

  static _setPrintSdkCallback(){
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, NiimbotPrinter?>((v) => v.connectedPrinter, (oldVal, newVal) {
      if(oldVal != null && newVal == null){
        disconnectCallback?.call();
      }
    });
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, int?>((v) => v.electricity, (oldValue, newValue) {
      if(newValue != null && oldValue != newValue){
        electricityCallback?.call(newValue);
      }
    });
    NiimbotPrintSDK.coverStatusCallback = coverStatusCallback;
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, int?>((v) => v.paperStatus, (oldValue, newValue) {
      if(newValue != null && oldValue != newValue){
        paperStatusCallback?.call(newValue);
      }
    });
    NiimbotPrintSDK.paperRfidStatusCallback = paperRfidStatusCallback;
    NiimbotPrintSDK.ribbonRfidStatusCallback = ribbonRfidStatusCallback;
    // NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, int?>((v) => v.paperRfidStatus, (oldValue, newValue) {
    //   if(newValue != null && oldValue != newValue){
    //     paperRfidStatusCallback?.call(newValue);
    //   }
    // });
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, int?>((v) => v.ribbonStatus, (oldValue, newValue) {
      if(newValue != null && oldValue != newValue){
        ribbonStatusCallback?.call(newValue);
      }
    });
    // NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, int?>((v) => v.ribbonRfidStatus, (oldValue, newValue) {
    //   if(newValue != null && oldValue != newValue){
    //     ribbonRfidStatusCallback?.call(newValue);
    //   }
    // });
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, int?>((v) => v.wifiRssi, (oldValue, newValue) {
      if(newValue != null && oldValue != newValue){
        wifiRssiCallback?.call(newValue);
      }
    });
  }
}

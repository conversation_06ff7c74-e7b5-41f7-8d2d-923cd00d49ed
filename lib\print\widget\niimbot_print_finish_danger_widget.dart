import 'package:flutter/material.dart';

import '../../utils/svg_icon.dart';
import '../print_manager.dart';
import 'niimbot_print_button.dart';

class NiimbotPrintFinishDangerWidget extends StatefulWidget {
  final bool isRePrint;

  const NiimbotPrintFinishDangerWidget(this.isRePrint, {super.key});

  @override
  _NiimbotPrintFinishDangerWidgetState createState() => _NiimbotPrintFinishDangerWidgetState();
}

class _NiimbotPrintFinishDangerWidgetState extends State<NiimbotPrintFinishDangerWidget> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 4500), () {
      Navigator.pop(context);  // 关闭当前对话框
    });
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.sizeOf(context).width;

    return GestureDetector(
      onTap: (){
        Navigator.pop(context);
      },
      child: Stack(
        children: [
          Container(
            color: const Color.fromRGBO(0, 0, 8, 0.1),
          ),
          Positioned(
            left: 14,
            bottom: 30,
            child: GestureDetector(
              onTap: (){},
              child: Container(
                  width: width - 28,
                  decoration: const BoxDecoration(
                      color: Colors.white, borderRadius: BorderRadiusDirectional.all(Radius.circular(14))),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 20, top: 20, bottom: 20, right: 20),
                    child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(
                            height: 45,
                          ),
                          const SvgIcon(
                            'assets/printSuccess.svg',
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          const SizedBox(
                              child: Center(
                                child: Text(
                                  '打印完成',
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Color(0xFF262626)),
                                ),
                              )),
                          ..._buildBottomWidget(context, width - 28),
                        ]),
                  )),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBottomWidget(BuildContext context, double width) {
    List<Widget> widgets = [];
    if (widget.isRePrint) {
      widgets.add(const SizedBox(
        height: 45,
      ));
      return widgets;
    }
    widgets.add(const SizedBox(
      height: 20,
    ));
    widgets.add(NiimbotPrintButton(
        height: 44,
        width: width,
        type: NiimbotButtonType.secondary,
        isLoading: false,
        text: PrintManager().getI18nString("app100001808", "查看危废台账"),
        onPressed: () {
          _checkDangerRecord(context);
        }));
    return widgets;
  }

  void _checkDangerRecord(BuildContext context) {
    Navigator.of(context).pop();
    PrintManager().printSettingLogic.closePrintSettingDialog();
    PrintManager().printSettingLogic.channel.checkDangerRecord();
  }
}

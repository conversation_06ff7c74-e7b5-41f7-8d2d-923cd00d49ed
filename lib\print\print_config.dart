import 'package:flutter/cupertino.dart';
import 'package:nety/exceptions/niimbot_nety_exception.dart';
import 'package:nety/models/index.dart';

class PrintConfig extends ChangeNotifier {
  /// 打印模式
  NiimbotPrintMode? printMode;

  /// 打印颜色模式
  NiimbotPrintColorMode? colorMode;

  /// 质量模式
  NiimbotPrintQualityMode? qualityMode;

  /// 打印异常状态
  NiimbotNetyExceptionCode? printError;

  /// 多页打印模式下的总页数
  final int? total;
  final int? _pageTotal;

  int? get pageTotal => _pageTotal;

  /// 多页打印模式下的当前页数
  final int? page;
  int _copies;

  /// 打印份数
  int get copies => _copies;

  int _density;

  int originalDensity = 1;

  /// 打印浓度
  int get density => _density;

  /// 打印浓度范围
  final List<int> densityRange;

  Offset _printOffset;

  /// 打印偏移校准
  Offset get printOffset => _printOffset;

  List<int>? _printRange;

  /// 获取打印范围
  /// [起始页,结束页]
  List<int>? get printRange => _printRange;

  /// 实际打印总页数
  int get printCount => (printRange != null ? printRange!.last - printRange!.first : 0) + 1;

  PrintConfig({
    int? copies,
    required int density,
    required this.densityRange,
    required this.originalDensity,
    Offset? printOffset,
    int? pageTotal,
    this.total,
    this.page,
    this.printMode,
    this.qualityMode,
    this.colorMode,
  })  : assert(densityRange.length == 2, '打印浓度范围长度必须为2'),
        assert((total != null && page != null) || total == null && page == null, 'total与page必须同时为空 或 同时不为空'),
        _copies = copies ?? 1,
        _density = density,
        _printOffset = printOffset ?? Offset.zero,
        _printRange = total != null && page != null ? [page, total] : null,
        _pageTotal = pageTotal ?? total;

  /// 更新打印质量
  void updatePrintQualityMode(NiimbotPrintQualityMode val) {
    qualityMode = val;
    notifyListeners();
  }

  /// 更新打印份数
  void updateCopies(int copies) {
    _copies = copies;
    notifyListeners();
  }

  /// 更新打印份数
  void updateDensity(int density) {
    _density = density;
    notifyListeners();
  }

  /// 更新打印机相关的偏移缓存等
  void _updateIsarPrintModel() async {}

  /// 更新水平偏移校准
  void updateHorizontalOffset(double horizontal) {
    _printOffset = Offset(horizontal, _printOffset.dy);
    _updateIsarPrintModel();
    notifyListeners();
  }

  /// 更新垂直偏移校准
  void updateVerticalOffset(double vertical) {
    _printOffset = Offset(_printOffset.dx, vertical);
    _updateIsarPrintModel();
    notifyListeners();
  }

  /// 更新打印起始页
  void updatePrintStart(int start) {
    if (_printRange != null) {
      _printRange = [start, _printRange!.last];
      notifyListeners();
    }
  }

  /// 更新打印起始页
  void updatePrintEnd(int end) {
    if (_printRange != null) {
      _printRange = [_printRange!.first, end];
      notifyListeners();
    }
  }

  /// 更新打印通信过程中的状态
  void updatePrintError(NiimbotNetyExceptionCode? code) {
    printError = code;
    if (printError != null) notifyListeners();
  }
}

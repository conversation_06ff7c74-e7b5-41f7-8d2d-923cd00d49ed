import 'dart:io';

import 'package:flutter/material.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/color.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';
import 'package:niimbot_template/models/template_data.dart';
typedef LabelReplaceCallback = void Function(bool isConfirm, bool checked);
class LabelReplaceWidget extends StatelessWidget {
  PrintSettingLogic logic;
  LabelReplaceCallback callback;
  bool _checked = false;
  late bool isMutipleBack;
  File? localImage;
  late String thumbnail;
  late String name;
  TemplateData templateData;

  LabelReplaceWidget({
    super.key,
    required this.logic,
    required this.templateData,
    required this.callback,
  }) {
    List<String> localBackground = templateData.localBackground ?? [];
    // 是否多背景
    isMutipleBack = localBackground.length > 1;
    localImage = localBackground.isNotEmpty ? File(localBackground[0]) : null;
    thumbnail = templateData.thumbnail ?? "";
    name = templateData?.name ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async{
        callback.call(false, _checked);
        return true;
      },
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {},
        child: Center(
          child: Container(
            width: 270,
            decoration:
                const BoxDecoration(color: Colors.white, borderRadius: BorderRadiusDirectional.all(Radius.circular(14))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Stack(
                  alignment: AlignmentDirectional.topEnd,
                  fit: StackFit.passthrough,
                  children: [
                    Positioned(
                      child: Row(
                        children: [
                          const Spacer(),
                          GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                Navigator.pop(context);
                                callback.call(false, _checked);
                              },
                              child: Container(
                                color: Colors.transparent,
                                padding: const EdgeInsetsDirectional.fromSTEB(30, 8, 9, 30),
                                child: const SvgIcon('assets/icon_rfid_dialog_close.svg'),
                              )),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(30, 25, 30, 12),
                      child: Text(logic.getI18nString("app100000533", "识别到已安装的标签纸"),
                          textAlign: TextAlign.center,
                          softWrap: true,
                          style: const TextStyle(
                              color: KColor.title,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.none)),
                    ),
                  ],
                ),
                Container(
                  height: 105,
                  margin: const EdgeInsetsDirectional.only(top: 0, bottom: 4, end: 45, start: 45),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: KColor.COLOR_FAFAFA,
                  ),
                  child: Center(
                      child: isMutipleBack || localImage == null
                          ? Image.network(
                              thumbnail,
                              errorBuilder: (context, error, _) {
                                return const SvgIcon('assets/label_placeholder.svg');
                              },
                              fit: BoxFit.contain,
                              height: 80,
                            )
                          : Image.file(
                              localImage!,
                              height: 80,
                              fit: BoxFit.contain,
                            )),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(name,
                      textAlign: TextAlign.center,
                      softWrap: true,
                      style: const TextStyle(
                          color: KColor.COLOR_999999,
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          decoration: TextDecoration.none)),
                ),
                Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 14, 16, 0),
                    child: Text(logic.getI18nString("app100000534", "为保证打印效果，建议使用此标签纸"),
                        textAlign: TextAlign.center,
                        softWrap: true,
                        style: const TextStyle(
                            color: KColor.BLACK,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            decoration: TextDecoration.none))),
                StatefulBuilder(
                  builder: (BuildContext context, void Function(void Function()) setState) {
                    return GestureDetector(
                        onTap: () {
                          setState(() {
                            _checked = !_checked;
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsetsDirectional.all(16),
                          child: Row(
                            children: [
                              Image.asset(
                                _checked ? "assets/ic_check_checked.png" : "assets/ic_check_default.png",
                                package: "niimbot_print_setting_plugin",
                                width: 16,
                                height: 16,
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              Expanded(
                                child: Text(logic.getI18nString("app100000535", "以后默认使用已识别到的标签纸"),
                                    softWrap: true,
                                    style: const TextStyle(
                                        color: KColor.COLOR_999999,
                                        fontSize: 13,
                                        fontWeight: FontWeight.w400,
                                        decoration: TextDecoration.none)),
                              ),
                            ],
                          ),
                        ));
                  },
                ),
                GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      callback.call(true, _checked);
                    },
                    child: Container(
                        margin: const EdgeInsetsDirectional.fromSTEB(20, 0, 20, 20),
                        padding: const EdgeInsets.symmetric(vertical: 11),
                        decoration: BoxDecoration(
                          color: KColor.RED,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(logic.getI18nString("app100000536", "使用"),
                              softWrap: true,
                              style: const TextStyle(
                                  color: KColor.WHITE,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  decoration: TextDecoration.none)),
                        ))),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

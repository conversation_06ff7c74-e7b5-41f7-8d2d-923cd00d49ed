import 'package:flutter/material.dart';

class ImageSliderThumb extends SliderComponentShape {
  final Size size;
  const ImageSliderThumb({Size? size}) : size = size ?? const Size(20, 20);

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return size;
  }

  @override
  void paint(PaintingContext context, Offset center,
      {required Animation<double> activationAnimation,
      required Animation<double> enableAnimation,
      required bool isDiscrete,
      required TextPainter labelPainter,
      required RenderBox parentBox,
      required SliderThemeData sliderTheme,
      required TextDirection textDirection,
      required double value,
      required double textScaleFactor,
      required Size sizeWithOverflow}) {
    Paint circlePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    double radius = size.width / 2;
    Paint borderPaint = Paint()
      ..color = Color(0xFFD9D9D9)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    context.canvas.drawShadow(
      Path()..addOval(Rect.fromCircle(center: Offset(center.dx / 1, center.dy / 1), radius: size.width / 2)),
      Colors.black.withOpacity(0.6), // shadow color, adjust opacity as needed
      5.0, // blur radius
      true, // whether to include shape
    );
    context.canvas.drawCircle(center, radius, circlePaint);
    context.canvas.drawCircle(center, radius, borderPaint);
  }
}

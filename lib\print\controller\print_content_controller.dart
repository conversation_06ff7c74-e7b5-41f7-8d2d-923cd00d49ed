import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/template_generate.dart';

import '../../Print_setting_logic.dart';
import '../../utils/PrintArea.dart';
import '../print_config.dart';

/// 打印内容管理控制器
class PrintContentController {
  //获取当前打印内容二值化数据
  Future<NetalImageResult> currentPrintTemplateData(
      PrintConfig printConfig, PrintSettingLogic logic, int index, double multiple, bool isSupportGray16) async {
    TemplateData templateData;
    if (logic.taskType == PrintTaskType.batch) {
      if (logic.printData.isJustOne) {
        templateData = await logic.getBatchPrintTemplateWith(logic.pageManager.style.pageIndex);
      } else {
        templateData = await logic.getBatchPrintTemplateWith(index - 1);
      }
    } else if (logic.taskType == PrintTaskType.printC1) {
      templateData = await logic.getBatchPrintC1Template(index - 1);
    } else {
      templateData = logic.parameter!.templateMoudleNew!;
    }
    //获取打印盲区
    List<double> printAreaSideList = await getPrintArea(logic, templateData);
    int page = index + (printConfig.printRange?.first ?? 1) - 1;
    TemplateData printTemplateData = templateData.copyWith();
    if (logic.parameter!.pdfBindInfo != null || !isSupportGray16) {
      List<BaseElement> baseElements = [];
      for (var element in printTemplateData.elements) {
        if (element is ImageElement) {
          ImageElement imageElement = element.copyWith();
          if (element.imageProcessingType == NetalImageRenderType.gradient && !isSupportGray16) {
            imageElement =
                imageElement.copyWith(imageProcessingType: NetalImageRenderType.grayscale, imageProcessingValue: [5]);
          }
          if (logic.parameter?.pdfBindInfo != null && logic.parameter!.pdfBindInfo!.elementIsBindPdf(imageElement.id)) {
            List<String> imagePaths = logic.parameter!.pdfBindInfo!.getPDFImagePathsWithElementId(element.id);
            String localImagePath = page - 1 >= imagePaths.length ? "" : imagePaths[page - 1];
            imageElement = imageElement.copyWith(localImageUrl: localImagePath);
            baseElements.add(imageElement);
          } else {
            baseElements.add(imageElement);
          }
        } else {
          baseElements.add(element);
        }
      }
      // printTemplateData = logic.parameter!.templateMoudleNew!.copyWith(elements: baseElements);
      printTemplateData = printTemplateData.copyWith(elements: baseElements);
    }
    //调用NetalPlugin插件异步获取打印二值化数据（同步获取方法废弃、会早晨打印速度慢等问题）
    final image = await NetalPlugin().generatePrintAsync(
      size: printTemplateData.size,
      elements: TemplateGenerate.generateNetalElements(printTemplateData, page: page),
      cableDirection: printTemplateData.cableDirection ?? NetalCableDirection.top,
      cableLength: printTemplateData.isCable ? printTemplateData.cableLength.toDouble() : 0,
      ratio: multiple,
      offset: printConfig.printOffset,
      orientation: printTemplateData.rotate.toInt(), // 由于业务层定义的旋转角度是逆时针，所以需要取反
    );
    debugPrint("YMY：当前绘制打印图返回第$index张");
    return Future.value(image);
  }

  ///获取打印盲区
  Future<List<double>> getPrintArea(PrintSettingLogic logic, TemplateData? templateData) async {
    if (templateData == null) {
      return [0, 0, 0, 0];
    }
    var seriesInfo = await logic.channel.getPrinterSeriesInfo(templateData.profile.machineId ?? "");
    List<int> areaList = PrintUtils.getPrintArea(
        TemplateArea(
            rotate: templateData.rotate,
            width: templateData.width,
            height: templateData.height,
            cableLength: templateData.cableLength,
            cableDirection: templateData.cableDirection!.value,
            canvasRotate: templateData.canvasRotate),
        200,
        jsonDecode(seriesInfo),
        logic.parameter?.templateMoudle,
        isForPrint: true);
    return areaList.map((element) {
      return element.toDouble();
    }).toList();
  }

  /// 获取打印颜色
  Color? _getPrintColor(PrintSettingLogic logic) {
    Map<String, dynamic> colorInfo = logic.channel.getPaperRibbonColor();
    String paperColor = colorInfo["paperColor"] ?? "";
    String ribbonColor = colorInfo["ribbonColor"] ?? "";
    List<int> rgb = [];
    if (ribbonColor.isNotEmpty && ribbonColor.split(".").length == 3) {
      rgb = ribbonColor.split(".").map((element) {
        return int.parse(element);
      }).toList();
    } else if (paperColor.isNotEmpty && paperColor.split(".").length == 3) {
      rgb = paperColor.split(".").map((element) {
        return int.parse(element);
      }).toList();
    }
    if (rgb.isNotEmpty) {
      return Color.fromARGB(255, rgb[0], rgb[1], rgb[2]);
    }
    return null;
  }
}

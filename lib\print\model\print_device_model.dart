class PrintDeviceModel {
  // Id id;
  String id;
  String seriesId;
  String seriesName;
  List<int> codes;
  String name;
  int status;
  int interfaceType;
  String interfaceName;
  int printDirection;
  double defaultWidth;
  double defaultHeight;
  int printMethodCode;
  String printMethodName;
  String securityAction;
  int solubilitySetType;
  List<ConsumablesModel> consumables;
  String modelName;
  String paperType;
  String? thumb;
  int displayBootPage;
  List<CompatibleWithApplicationsModel> compatibleWithApplications;
  double maxPrintHeight;
  double maxPrintWidth;
  bool isSupportCalibration;
  bool isSupportWifi;
  String paccuracyName;
  int paccuracy;
  List<String> rfidNotSupportVersions; // 假设rfid不支持的版本列表是动态类型
  int rfidType;
  int solubilitySetDefault;
  int solubilitySetEnd;
  int solubilitySetStart;
  int widthSetStart;
  int widthSetEnd;
  List<String> wifiNotSupportVersions; // 假设wifi不支持的版本列表是动态类型

  PrintDeviceModel({
    required this.id,
    required this.seriesId,
    required this.seriesName,
    required this.codes,
    required this.name,
    required this.status,
    required this.interfaceType,
    required this.interfaceName,
    required this.printDirection,
    required this.defaultWidth,
    required this.defaultHeight,
    required this.printMethodCode,
    required this.printMethodName,
    required this.securityAction,
    required this.solubilitySetType,
    required this.consumables,
    required this.modelName,
    required this.paperType,
    this.thumb,
    required this.displayBootPage,
    required this.compatibleWithApplications,
    required this.maxPrintHeight,
    required this.maxPrintWidth,
    required this.isSupportCalibration,
    required this.isSupportWifi,
    required this.paccuracyName,
    required this.paccuracy,
    required this.rfidNotSupportVersions,
    required this.rfidType,
    required this.solubilitySetDefault,
    required this.solubilitySetEnd,
    required this.solubilitySetStart,
    required this.widthSetStart,
    required this.widthSetEnd,
    required this.wifiNotSupportVersions,
  });

  static List<int> toListInt(codes) {
    List<int> listInt = [];
    for (var e in codes) {
      listInt.add(e);
    }
    return listInt;
  }

  static List<String> toListString(codes) {
    List<String> list = [];
    for (var e in codes) {
      list.add(e);
    }
    return list;
  }

  static List<ConsumablesModel> toConsumablesModel(data) {
    List<ConsumablesModel> list = [];
    for (var e in data) {
      list.add(ConsumablesModel.fromJson(e));
    }
    return list;
  }

  static List<CompatibleWithApplicationsModel> toCompatibleWithApplicationsModel(data) {
    List<CompatibleWithApplicationsModel> list = [];
    for (var e in data) {
      list.add(CompatibleWithApplicationsModel.fromJson(e));
    }
    return list;
  }

  // 从JSON字符串创建Model对象
  PrintDeviceModel.fromJson(Map<String, dynamic> json)
      : id = json["id"],
        seriesId = json["seriesId"] ?? "",
        seriesName = json["seriesName"] ?? "",
        codes = toListInt(json["codes"]) ?? [],
        name = json["name"] ?? "",
        status = json["status"] ?? 0,
        interfaceType = json["interfaceType"] ?? 0,
        interfaceName = json["interfaceName"] ?? "",
        printDirection = json["printDirection"] ?? 0,
        defaultWidth = json["defaultWidth"]?.toDouble() ?? 0.0,
        defaultHeight = json["defaultHeigth"]?.toDouble() ?? 0.0,
        printMethodCode = json["printMethodCode"] ?? 0,
        printMethodName = json["printMethodName"] ?? "",
        securityAction = json["securityAction"].toString() ?? "",
        solubilitySetType = json["solubilitySetType"] ?? 0,
        consumables = toConsumablesModel(json["consumables"]),
        modelName = json["modelName"] ?? "",
        paperType = json["paperType"] ?? "",
        thumb = json["thumb"],
        displayBootPage = json["displayBootPage"] ?? 0,
        compatibleWithApplications = toCompatibleWithApplicationsModel(json['compatibleWithApplications']),
        maxPrintHeight = json["maxPrintHeight"]?.toDouble() ?? 0.0,
        maxPrintWidth = json["maxPrintWidth"]?.toDouble() ?? 0.0,
        isSupportCalibration = json["isSupportCalibration"] ?? false,
        isSupportWifi = json["isSupportWifi"] ?? false,
        paccuracyName = json["paccuracyName"] ?? '',
        paccuracy = json["paccuracy"] ?? 0,
        rfidNotSupportVersions = toListString(json["rfidNotSupportVersions"]),
        rfidType = json["rfidType"] ?? 0,
        solubilitySetDefault = json["solubilitySetDefault"] ?? 0,
        solubilitySetEnd = json["solubilitySetEnd"] ?? 0,
        solubilitySetStart = json["solubilitySetStart"] ?? 0,
        widthSetStart = json["widthSetStart"] ?? 0,
        widthSetEnd = json["widthSetEnd"] ?? 0,
        wifiNotSupportVersions = toListString(json["wifiNotSupportVersions"]);

  // 将Model对象转换为Map
  Map<String, dynamic> toJson() => {
        "id": id,
        "seriesId": seriesId,
        "seriesName": seriesName,
        "codes": codes,
        "name": name,
        "status": status,
        "interfaceType": interfaceType,
        "interfaceName": interfaceName,
        "printDirection": printDirection,
        "defaultWidth": defaultWidth,
        "defaultHeigth": defaultHeight, // 注意字段名
        "printMethodCode": printMethodCode,
        "printMethodName": printMethodName,
        "securityAction": securityAction,
        "solubilitySetType": solubilitySetType,
        "modelName": modelName,
        "paperType": paperType,
        "thumb": thumb,
        "displayBootPage": displayBootPage,
        "compatibleWithApplications": compatibleWithApplications,
        "maxPrintHeight": maxPrintHeight, // 注意字段名
        "maxPrintWidth": maxPrintWidth,
        "isSupportCalibration": isSupportCalibration,
        "isSupportWifi": isSupportWifi,
        "paccuracyName": paccuracyName,
        "paccuracy": paccuracy,
        "rfidNotSupportVersions": rfidNotSupportVersions,
        "rfidType": rfidType,
        "solubilitySetDefault": solubilitySetDefault,
        "solubilitySetEnd": solubilitySetEnd,
        "solubilitySetStart": solubilitySetStart, // 注意字段名
        "widthSetStart": widthSetStart,
        "widthSetEnd": widthSetEnd,
        "wifiNotSupportVersions": wifiNotSupportVersions,
      };

  int fastHash(String string) {
    var hash = 0xcbf29ce484222325;
    var i = 0;
    while (i < string.length) {
      final codeUnit = string.codeUnitAt(i++);
      hash ^= codeUnit >> 8;
      hash *= 0x100000001b3;
      hash ^= codeUnit & 0xFF;
      hash *= 0x100000001b3;
    }
    return hash;
  }
}

class ParentPropertyModel {
  String? name;
  String? density;
  int? code;
  String? multilingualCode;
  String? printModeValue;
  String? printModeName;

  ParentPropertyModel({
    this.name,
    this.density,
    this.code,
    this.multilingualCode,
    this.printModeValue,
    this.printModeName,
  });

  // 从JSON字符串创建Model对象
  ParentPropertyModel.fromJson(Map<String, dynamic> json)
      : name = json["name"] ?? "",
        density = json["density"].toString(),
        code = json["code"],
        multilingualCode = json["multilingualCode"] ?? "",
        printModeValue = json["printModeValue"] ?? "",
        printModeName = json["printModeName"] ?? "";

  // 将Model对象转换为Map
  Map<String, dynamic> toJson() => {
        "name": name,
        "density": density,
        "code": code,
        "multilingualCode": multilingualCode,
        "printModeValue": printModeValue,
        "printModeName": printModeName
      };
}

class ChildPropertiesModel {
  String? name;
  int? code;
  String? multilingualCode;
  String? blindZone;

  ChildPropertiesModel({
    this.name,
    this.code,
    this.multilingualCode,
    this.blindZone,
  });

  // 从JSON字符串创建Model对象
  ChildPropertiesModel.fromJson(Map<String, dynamic> json)
      : name = json["name"] ?? "",
        code = json["code"] ?? 0,
        multilingualCode = json["multilingualCode"] ?? "",
        blindZone = json["blindZone"] ?? "";

  // 将Model对象转换为Map
  Map<String, dynamic> toJson() => {
        "name": name,
        "code": code,
        "multilingualCode": multilingualCode,
        "blindZone": blindZone,
      };
}

class ConsumablesModel {
  ParentPropertyModel? parentProperty;
  List<ChildPropertiesModel>? childProperties;

  ConsumablesModel({
    this.parentProperty,
    this.childProperties,
  });

  static List<ChildPropertiesModel> toChildProperties(data) {
    List<ChildPropertiesModel> list = [];
    for (var e in data) {
      list.add(ChildPropertiesModel.fromJson(e));
    }
    return list;
  }

  // 从JSON字符串创建Model对象
  ConsumablesModel.fromJson(Map<String, dynamic> json)
      : parentProperty = ParentPropertyModel.fromJson(json["parentProperty"]),
        childProperties = toChildProperties(json["childProperties"]);

  // 将Model对象转换为Map
  Map<String, dynamic> toJson() => {
        "parentProperty": parentProperty?.toJson(),
        "childProperties": childProperties?.map((e) => e.toJson()),
      };
}

class CompatibleWithApplicationsModel {
  String? name;
  String? code;

  CompatibleWithApplicationsModel({
    this.name,
    this.code,
  });

  // 从JSON字符串创建Model对象
  CompatibleWithApplicationsModel.fromJson(Map<String, dynamic> json)
      : name = json["name"] ?? "",
        code = json["code"] ?? "";

  // 将Model对象转换为Map
  Map<String, dynamic> toJson() => {
        "name": name,
        "code": code,
      };
}

import 'dart:convert';

import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_print_setting_plugin/model/pdf_bind_info.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_template/niimbot_template.dart';

class PrintParameter {
  // 是否重置显示多个
  bool? resetDisplayMultiple;

  // 打印历史ID，用于标识打印任务
  String? printHistoryId;

  // 打印次数，可能用于控制打印输出的数量
  int printCount = 1;

  // 任务类型，预设为普通类型，可能影响打印任务的处理方式
  PrintTaskType? taskType;

  // 模板模块，可能包含打印任务的详细配置信息
  Map<String, dynamic>? templateMoudle;

  // 模板模块，可能包含打印任务的详细配置信息
  TemplateData? templateMoudleNew;

  // 是否显示RFID绑定提示，可能用于控制界面元素的显示与隐藏
  bool? showRfidBind;
  RfidInfo? rfidInfo;
  // 批量打印模版ID
  List<Map<String, dynamic>>? batchtIds;

  // 批量打印图片 图片地址

  PdfBindInfo? pdfBindInfo;

  // 自定义数据，用于存储打印任务中可能用到的额外信息
  Map<String, dynamic>? customData;

  bool isLabelRecord = false;

  PrintParameter.fromJson(Map<String, dynamic> json) {
    String? jsonString = "";
    Map<String, dynamic> niimbotTemplate = {};
    if (json["niimbotTemplate"] != null && json["niimbotTemplate"] is Map) {
      templateMoudle = Map<String, dynamic>.from(json["niimbotTemplate"]);
    } else {
      jsonString = json.remove('niimbotTemplate');
      if (jsonString != null) {
        niimbotTemplate = jsonDecode(jsonString);
        if (niimbotTemplate["niimbotTemplate"] != null) {
          templateMoudle = niimbotTemplate["niimbotTemplate"];
        } else {
          templateMoudle = niimbotTemplate;
        }
      }
    }

    if (templateMoudle != null &&
        templateMoudle!['elements'] != null &&
        templateMoudle!['elements'] is List &&
        taskType == PrintTaskType.miniApp) {
      for (var element in templateMoudle!['elements']) {
        if (element['value'] == null || element['value'] == '') {
          element['value'] = ' ';
        }
      }
    }

    resetDisplayMultiple = json.remove('resetDisplayMultiple') ?? false;
    printHistoryId = json.remove('printHistoryId');
    printCount = json.remove('printCount') ?? 0;

    // 解析batchtIds的JSON数据
    if (json["batchtIds"] != null) {
      final List<dynamic> jsonData = jsonDecode(json["batchtIds"]);
      batchtIds = jsonData.map((item) => item as Map<String, dynamic>).toList();
    }
    // 设置是否显示RFID绑定
    showRfidBind = json.remove('showRfidBind') ?? false;
    // 解析并设置RFID信息
    if (json["rfidInfo"] != null && json["rfidInfo"] != "") {
      Map rfidMap = jsonDecode(json.remove("rfidInfo"));
      rfidInfo = RfidInfo.fromJson(rfidMap);
      CellAddress cellAddress = NiimbotExcelUtils.decodeCellIndex(rfidInfo!.value);
      RfidInfoManager().setRfidBindingColumn(cellAddress.c);
    } else {
      RfidInfoManager().setRfidBindingColumn(null);
    }
    // 合并niimbotTemplate到customData
    customData = json;
    if (jsonString != null) {
      customData?.addAll(Map<String, dynamic>.from(niimbotTemplate));
    }
    // 根据printScene设置taskType
    taskType = PrintTaskTypeExtension.fromScene(json["printScene"] ?? "");
    if(taskType == PrintTaskType.printC1) {
      batchtIds = [];
      int paragraphCount = json["paragraphCount"];
      for(int i = 0; i < paragraphCount; i++) {
        batchtIds!.add({"id": i.toString(), "printPage": 1});
      }
    }
    // }
    if (json["pdfBindInfo"] != null) {
      List<Map<String, dynamic>> pdfBindElementInfos = [];
      for (var element in json["pdfBindInfo"]) {
        pdfBindElementInfos.add(Map<String, dynamic>.from(element));
      }
      pdfBindInfo = PdfBindInfo(pdfBindElementInfos);
    } else {
      pdfBindInfo = null;
    }
  }

  updateTemplateModule(TemplateData templateMoudleNew) {
    this.templateMoudleNew = templateMoudleNew;
    templateMoudle = templateMoudleNew.toJson();
    if (templateMoudle?['dataSource'] == null && templateMoudle?['dataSources'] != null) {
      templateMoudle?['dataSource'] = templateMoudle?['dataSources'];
    }
  }

  String templateMoudleToJson() {
    return jsonEncode(templateMoudle);
  }
}

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:nety/models/index.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_setting_plugin/model/c1_print_data.dart';
import 'package:niimbot_print_setting_plugin/print/c1_print_config.dart';
import 'package:niimbot_print_setting_plugin/print/printer_strategy_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/template_data.dart';

import '../model/print_device_model.dart';
import '../print_config.dart';
import '../print_manager.dart';

const String gray16ColorValue = "159.160.160";

///打印配置管理控制器
class PrintConfigController {
  /// 获取打印配置参数
  /// [printSettinglogic] 打印
  /// [printer] 当前连接的打印机
  Future<PrintConfig?> getPrintConfig(PrintSettingLogic printSettinglogic, NiimbotPrinter printer) async {
    try {
      TemplateData templateData = printSettinglogic.parameter!.templateMoudleNew!.copyWith();
      Map printerInfo =
          await printSettinglogic.channel.getCurrentPrinterInfo((printer.code ?? 0).toString(), printer.name ?? '');
      PrintDeviceModel currentDeviceModel = PrintDeviceModel.fromJson(Map<String, dynamic>.from(printerInfo));
      int total = printSettinglogic.printData.pageEnd;
      int page = printSettinglogic.printData.pageBegin;
      int? pageTotal;
      int copies = printSettinglogic.printData.printCount;
      //获取打印策略
      StrategyType strategyType = StrategyType.normal;
      if (printSettinglogic.taskType == PrintTaskType.printNullUi && printSettinglogic.printData.isShowLoading) {
        EasyLoading.show();
      }
      if (PrinterStrategyManager().isSupportRFIDPrint) {
        debugPrint("PrintStrategyLog2:执行RFID打印");
        //RFID打印

        List<NiimbotRFIDInfo> rfidInfos = await NiimbotPrintSDK().getConsumablesRFIDData(accurate: true);
        //风控
        printSettinglogic.channel.riskCheck(printSettinglogic.pageManager.context, 4, rfidInfos: rfidInfos);
        //打印策略
        strategyType = await PrinterStrategyManager()
            .printCheckRFIDPrintStrategy(printSettinglogic, PrintManager().uniqueValue, (languageCode, descrp) {
          return PrintManager().getI18nString(languageCode, descrp);
        }, rfidInfos: rfidInfos);
        if (strategyType == StrategyType.forbid) {
          if (printSettinglogic.taskType == PrintTaskType.printNullUi) EasyLoading.dismiss();
          printSettinglogic.printingStatusReset();
          return Future.value(null);
        } else if (strategyType == StrategyType.batchforbid) {
          total = 1;
          pageTotal = 1;
          copies = 1;
        }
      } else {
        //非RFID打印
        debugPrint("PrintStrategyLog2:执行非RFID打印");
      }
      //获取当前打印颜色类型 单色/灰阶/16色灰阶/双色
      if (printSettinglogic.taskType == PrintTaskType.printNullUi && printSettinglogic.printData.isShowLoading) {
        EasyLoading.dismiss();
      }
      Map<String, dynamic> colorInfo = printSettinglogic.channel.getPaperRibbonColor();
      debugPrint("PrintStrategyLog2--colorInfo:${jsonEncode(colorInfo)}");
      final colorMode = await _checkColorType(templateData, printer, colorInfo);
      //打印偏移
      Offset printOffset = Offset(printSettinglogic.printData.ofsetX, -printSettinglogic.printData.ofsetY);
      if (printSettinglogic.printData.taskType == PrintTaskType.printJustOne.name) {
        int pageIndex = printSettinglogic.style.pageIndex;
        page = pageIndex + 1;
        pageTotal = 1;
        total = page;
        copies = 1;
      }
      if (printSettinglogic.taskType == PrintTaskType.printNullUi ||
          printSettinglogic.taskType == PrintTaskType.printNullUiShowProgress) {
        var hardWare = await printSettinglogic.channel
            .getCurrentPrintDensityInfo(printSettinglogic.parameter!.templateMoudle!["consumableType"].toString());
        int solubilitySetEnd = hardWare["solubilitySetEnd"] ?? 0;
        int? customDensity = printSettinglogic.parameter!.customData?['density'];
        int printDensity = customDensity ?? (hardWare["density"] ?? 0);
        printSettinglogic.printData.printDensity = printDensity > solubilitySetEnd ? solubilitySetEnd : printDensity;
      }
      //获取打印浓度
      int printDensity = getDensityByStrategy(printSettinglogic.printData.printDensity, strategyType);
      NiimbotPrintMode printModel = NiimbotPrintMode.thermal;
      if (currentDeviceModel.modelName == '热敏打印机') {
        printModel = NiimbotPrintMode.thermal;
      } else if (currentDeviceModel.modelName == '热转印打印机') {
        printModel = NiimbotPrintMode.ribbon;
      } else {
        for (ConsumablesModel element in currentDeviceModel.consumables) {
          if (printSettinglogic.parameter!.templateMoudleNew!.consumableType == element.parentProperty?.code) {
            printModel = int.parse(element.parentProperty!.printModeValue ?? "1") == 1
                ? NiimbotPrintMode.thermal
                : NiimbotPrintMode.ribbon;
            break;
          }
        }
      }
      // 根据PrintData生成不同的打印配置
      PrintConfig printConfig = PrintConfig(
        copies: copies,
        originalDensity: printSettinglogic.printData.printDensity,
        density: printDensity,
        densityRange: [currentDeviceModel.solubilitySetStart, currentDeviceModel.solubilitySetEnd],
        pageTotal: pageTotal,
        total: total > 1 ? total : null,
        page: total > 1 ? page : null,
        printMode: printModel,
        qualityMode: printSettinglogic.printData.printPriority == 0
            ? NiimbotPrintQualityMode.quality
            : NiimbotPrintQualityMode.speed,
        colorMode: colorMode,
        printOffset: printOffset,
      );
      if (printSettinglogic.printData is C1PrintData) {
        printConfig = C1PrintConfig(
          copies: copies,
          originalDensity: printSettinglogic.printData.printDensity,
          density: printDensity,
          densityRange: [currentDeviceModel.solubilitySetStart, currentDeviceModel.solubilitySetEnd],
          pageTotal: pageTotal,
          total: total > 1 ? total : null,
          page: total > 1 ? page : null,
          printMode: printModel,
          qualityMode: printSettinglogic.printData.printPriority == 0
              ? NiimbotPrintQualityMode.quality
              : NiimbotPrintQualityMode.speed,
          colorMode: colorMode,
          printOffset: printOffset,
          cutType: (printSettinglogic.printData as C1PrintData).cutType,
          tubeType: (printSettinglogic.printData as C1PrintData).tubeType,
          tubeSpecs: (printSettinglogic.printData as C1PrintData).tubeSpecs,
          isHalfCut: (printSettinglogic.printData as C1PrintData).isHalfCut,
          cutDepth: (printSettinglogic.printData as C1PrintData).cutDepth,
        );
      }
      return Future.value(printConfig);
    } catch (e) {
      return Future.value(null);
    }
  }

  ///获取打印浓度
  int getDensityByStrategy(int printDensity, StrategyType type) {
    //此处根据打印策略设置浓度
    int density = printDensity;
    if (type == StrategyType.lowDensity0) {
      density = 0;
    } else if (type == StrategyType.lowDensity1) {
      density = -1;
    } else if (type == StrategyType.lowDensity2) {
      density = -2;
    }
    return density;
  }

  // 获取当前打印颜色类型
  Future<NiimbotPrintColorMode> _checkColorType(
      TemplateData data, NiimbotPrinter connectedPrinter, Map<String, dynamic> colorInfo) async {
    final elements = data.elements;
    // 是否包含多色
    final Set<Color> colors = {};
    // 是否包含灰阶图片
    var hasGray = false;
    var hasGray16 = false;
    for (final e in elements) {
      if (e is ImageElement) {
        if (e.imageProcessingType == NetalImageRenderType.grayscale) {
          hasGray = true;
        } else if (e.imageProcessingType == NetalImageRenderType.gradient) {
          hasGray16 = true;
        }
      }
      if (!(colors.length > 1)) {
        /* 没有颜色数据 */
        if (e is ColorElement) {
          colors.add(e.elementColor);
        }
        if (e is TableElement) {
          colors.add(e.lineColor);
          colors.add(e.contentColor);
        }
      }
    }
    if (hasGray16 && isSupportCurrentColorModel(NiimbotPrintColorMode.gray16, colorInfo)) {
      return NiimbotPrintColorMode.gray16; //16色灰阶
    }
    if (isSupportCurrentColorModel(NiimbotPrintColorMode.multi, colorInfo)) {
      /* 打印机是否支持颜色 */
      return NiimbotPrintColorMode.multi; //多色打印
    }
    if (hasGray && isSupportCurrentColorModel(NiimbotPrintColorMode.gray, colorInfo)) {
      return NiimbotPrintColorMode.gray; //单色灰阶
    }
    return NiimbotPrintColorMode.single;
  }

  ///耗材及打印机是否支持当前颜色模式
  bool isSupportCurrentColorModel(NiimbotPrintColorMode colorModel, Map<String, dynamic> colorInfo) {
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrinter == null) return false;
    bool isSupportColorModel = true;
    String paperColor = colorInfo["paperColor"] ?? "";
    String ribbonColor = colorInfo["ribbonColor"] ?? "";
    debugPrint("PrintStrategyLog2--colorInfo-NiimbotPrintColorMode.multi support:${connectedPrinter.colorModeSupport?.contains(colorModel) ?? false}");
    switch (colorModel) {
      case NiimbotPrintColorMode.gray16: //16色灰阶
        isSupportColorModel = (paperColor == gray16ColorValue || ribbonColor == gray16ColorValue) &&
            (connectedPrinter.colorModeSupport?.contains(colorModel) ?? false);
        break;
      case NiimbotPrintColorMode.multi: //多色打印
        isSupportColorModel = (paperColor.split(',').length > 1 || ribbonColor.split(',').length > 1) &&
            (connectedPrinter.colorModeSupport?.contains(colorModel) ?? false);
        break;
      case NiimbotPrintColorMode.gray: //灰阶渐变
        isSupportColorModel = (connectedPrinter.colorModeSupport?.contains(NiimbotPrintColorMode.gray) ?? false);
        break;
      case NiimbotPrintColorMode.single: //单色
      default:
        isSupportColorModel = true;
    }
    return isSupportColorModel;
  }
}

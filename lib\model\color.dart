import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';

class KColor {
  static const Color COLOR_PRIMARY = WHITE; //默认主题色
  static const Color COLOR_999999 = Color(0xFF999999);
  static const Color WHITE = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color RED = Color(0xFFFB4B42);
  static const Color RED_DISABLE = Color(0x4CFB4B42);

  /// 通用主题色
  static const Color title = Color(0xFF262626);
  static const Color mainTitle = Color(0xFF000000);
  static const Color secone_title = Color(0xFF757575);
  static const Color pinglunTitle = Color(0xFF595959);
  static const Color pingAtTitle = Color(0xff537FB7);
  static const Color subtitle = Color(0xFF999999);
  static const Color disable_text = Color(0xFFD9D9D9);
  static const Color hint_text = Color(0xFFCCCCCC);
  static const Color content_background = Color(0xFFF5F5F5);
  static const Color theme = COLOR_PRIMARY;
  static const Color clear = Color(0x00000000);
  static const Color color_divider = Color(0xFFEBEBEB);
  static const Color link_color = Color(0xFF537FB7);

  static const Color hudBackgroundColor = Color(0xA6000000);
  static const Color guideBackgroundColor = Color(0x41000000);
  static const Color hudForegroundColor = Color(0xFFFFFFFF);

  static const Color goodsItemBackgroundColor = Color(0xFFFAFAFA);
  static const Color COLOR_262626 = Color(0xFF262626);
  static const Color BackgroundColor = Color(0xFFFAFAFA);

  static const Color COLOR_333333 = Color(0xFF333333);
  static const Color COLOR_595959 = Color(0xFF595959);
  static const Color COLOR_F5F5F5 = Color(0xFFF5F5F5);
  static const Color COLOR_FEF1D1 = Color(0xFFFEF1D1);
  static const Color COLOR_080808 = Color(0xFF080808);

  static const Color COLOR_F7F7F7 = Color(0xFFF7F7F7);

  static const Color curptino_blue = Color(0xFF2878ff);

  static const Color shadow_grey = Color(0xFFF0F0F0);
  static const Color COLOR_1E767680 = Color(0x1E767680);

  static const Color COLOR_F8F8F8 = Color(0xFFF8F8F8);
  static const Color COLOR_BFBFBF = Color(0xFFBFBFBF);
  static const Color COLOR_EEEEEE = Color(0xFFEEEEEE);
  static const Color COLOR_FAFAFA = Color(0xFFFAFAFA);
  static const Color BLACK = Color(0xFF000000);

  static const Color COLOR_FFEDEC = Color(0xFFFFEDEC);
  static const Color COLOR_FFF5F1 = Color(0xFFFFF5F1);
  static const Color COLOR_F7F7FA = Color(0xFFF7F7FA);

  static Color random() {
    return Color.fromARGB(255, Random().nextInt(255), Random().nextInt(255), Random().nextInt(255));
  }
}

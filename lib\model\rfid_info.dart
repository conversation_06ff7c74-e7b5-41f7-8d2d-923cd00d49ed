import 'package:niimbot_excel/models/data_source.dart';

///rfid数据源实体
class RfidInfo {
  List<DataSource> dataSource;
  String value;
  List<String> dataBind;

  RfidInfo({
    required this.dataSource,
    required this.value,
    required this.dataBind,
  });

  RfidInfo.fromJson(dynamic json)
      : dataSource = _parseDataSource(json['dataSource']),
        value = json['value'],
        dataBind = json['dataBind'] != null ? json['dataBind'].cast<String>() : [];

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['dataSource'] = dataSource.map((v) => v.toJson()).toList();
    map['value'] = value;
    map['dataBind'] = dataBind;
    return map;
  }
}

List<DataSource> _parseDataSource(dynamic field) {
  final List<DataSource> dataSource = [];
  if (field != null && field is List) {
    field.forEach((v) {
      dataSource.add(DataSource.fromJson(v));
    });
  }
  return dataSource;
}

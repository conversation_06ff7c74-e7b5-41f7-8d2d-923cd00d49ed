import 'package:flutter/cupertino.dart';

/// 打印类
class Logger {
  final String _name; // 基础打印信息筛选标志
  bool _debug = true;
  bool _on = true;

  Logger._internal(this._name) {
    _debug = true;
    _on = true;
  }

  static final Map<String, Logger> _cache = {};

  factory Logger(String name, {bool on = true}) {
    if (_cache.containsKey(name)) {
      _cache[name]?._on = on;
      return _cache[name]!;
    } else {
      var logger = Logger._internal(name);
      logger._on = on;
      _cache[name] = logger;
      return logger;
    }
  }

  static const int _maxLen = 128;

  void log(Object? msg, [String? tag]) {
    if (_debug && _on) {
      String log = msg?.toString() ?? 'null';
      if (log.length <= _maxLen) {
        debugPrint(
            "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}:  $_name => ${tag == null ? '' : '$tag: '}$msg");
        return;
      }

      debugPrint(
          "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second} $_name => ${tag ?? ''}\n"
          "------- st -------");

      while (log.isNotEmpty) {
        if (log.length > _maxLen) {
          debugPrint(log.substring(0, _maxLen));
          log = log.substring(_maxLen, log.length);
        } else {
          debugPrint(log);
          log = '';
        }
      }
      debugPrint('------- ed -------');
    }
  }
}

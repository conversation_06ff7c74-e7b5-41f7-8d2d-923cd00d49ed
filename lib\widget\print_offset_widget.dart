import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';

class PrintOffsetWidget extends StatefulWidget {
  PrintSettingLogic logic;

  PrintOffsetWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintOffsetWidgetState createState() => _PrintOffsetWidgetState();
}

class _PrintOffsetWidgetState extends State<PrintOffsetWidget> with WidgetsBindingObserver {
  double ofsetX = 0.0;
  double ofsetY = 0.0;
  FocusNode nodeX = FocusNode();
  FocusNode nodeY = FocusNode();
  double keyboardHeight = 0;
  bool isLongPress = false;
  TextEditingController? ofsetXController = TextEditingController();
  TextEditingController? ofsetYController = TextEditingController();
  Timer? _timer;
  double viewBottomSafeHeight = 0;
  @override
  void initState() {
    ofsetX = widget.logic.printData.ofsetX;
    ofsetY = widget.logic.printData.ofsetY;
    ofsetXController?.text = ofsetX.toStringAsFixed(1);
    ofsetYController?.text = ofsetY.toStringAsFixed(1);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      viewBottomSafeHeight = MediaQuery.of(context).padding.bottom;
      debugPrint("底部高度:$viewBottomSafeHeight");
    });

    nodeX.addListener(() {
      // 检查lessNode是否获得了焦点，如果没有，则执行以下代码
      if (!nodeX.hasFocus) {
        if (ofsetXController!.text.isEmpty) {
          ofsetXController!.text = "0.0";
        }
        double value = double.parse(ofsetXController!.text);
        ofsetXController!.text = value.toString();
        if (value > 5) {
          ofsetX = 5;
          ofsetXController!.text = "5";
        } else if (value < -5) {
          ofsetX = -5;
          ofsetXController!.text = "-5";
        }
        double offsetX = double.parse(ofsetXController!.text);
        ofsetX = offsetX;
        setState(() {});
      }
    });
    nodeY.addListener(() {
      if (ofsetYController!.text.isEmpty) {
        ofsetYController!.text = "0.0";
      }
      double value = double.parse(ofsetYController!.text);
      ofsetYController!.text = value.toString();
      if (value > 5) {
        ofsetYController!.text = "5";
        ofsetY = 5;
      } else if (value < -5) {
        ofsetY = -5;
        ofsetYController!.text = "-5";
      }
      double offsetY = double.parse(ofsetYController!.text);
      ofsetY = offsetY;
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    nodeX.dispose();
    nodeY.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    // 键盘高度
    final double viewInsetsBottom =
        EdgeInsets.fromViewPadding(View.of(context).viewInsets, View.of(context).devicePixelRatio).bottom;
    if (Platform.isIOS) {
      keyboardHeight = viewInsetsBottom - 30 + (viewBottomSafeHeight > 0?0:30*2);
    } else {
      keyboardHeight = viewInsetsBottom;
    }
    keyboardHeight = keyboardHeight / 2 + 45;
    // 打印键盘高度
    setState(() {});
  }

  KeyboardActionsConfig _buildConfig() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: nodeX,
          onTapAction: () {},
        ),
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: nodeY,
          onTapAction: () {},
        )
      ],
      defaultDoneWidget: GestureDetector(
        onTap: () {
          nodeX.unfocus();
          nodeY.unfocus();
        },
        child: Text(
          widget.logic.getI18nString('app01031', '完成'),
          style: const TextStyle(
            color: Color(0xFF262626),
            fontSize: 15.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.sizeOf(context).height * 0.4 + keyboardHeight + 20,
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.0),
          topRight: Radius.circular(12.0),
        ),
      ),
      // height: MediaQuery.of(context).size.height * 0.45,
      child: Column(
        children: [
          Container(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 16.0),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0),
              ),
            ),
            height: 48,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    widget.logic.getI18nString("login0062", "取消"),
                    style: TextStyle(color: Color(0xFF161616), fontSize: 16.0, fontWeight: FontWeight.w600),
                  ),
                ),
                Text(
                  widget.logic.getI18nString("app00977", "偏移校准"),
                  style: TextStyle(color: Color(0xFF161616), fontSize: 16.0, fontWeight: FontWeight.w600),
                ),
                GestureDetector(
                  onTap: () {
                    if (ofsetXController!.text.isEmpty) {
                      ofsetXController!.text = "0.0";
                    }
                    if (ofsetYController!.text.isEmpty) {
                      ofsetYController!.text = "0.0";
                    }
                    double offsetX = double.parse(ofsetXController!.text);
                    double offsetY = double.parse(ofsetYController!.text);
                    widget.logic.printData.ofsetX = offsetX;
                    widget.logic.printData.ofsetY = offsetY;
                    widget.logic.saveDeviceOffset(offsetY, offsetX);
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    widget.logic.getI18nString("app00017", "保存"),
                    style: TextStyle(
                        color: widget.logic.style.saveBtnBgColor, fontSize: 16.0, fontWeight: FontWeight.w600),
                  ),
                )
              ],
            ),
          ),
          _ofsetCalibration()
        ],
      ),
    );
  }

  _ofsetCalibration() {
    if (ofsetXController!.text.isEmpty) {
      ofsetXController!.text = "0.0";
    }
    if (ofsetYController!.text.isEmpty) {
      ofsetYController!.text = "0.0";
    }
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      margin: EdgeInsetsDirectional.symmetric(vertical: 12, horizontal: 16),
      padding: EdgeInsetsDirectional.symmetric(vertical: 12),
      child: Column(
        children: [
          Container(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Text(
                  widget.logic.getI18nString('app100001510', '水平偏移(mm)'),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                ),
                Expanded(child: Container()),
                GestureDetector(
                  onTap: () {
                    if (ofsetX > -5) {
                      ofsetX = double.parse((ofsetX - 0.1).toStringAsFixed(1));
                      ofsetXController?.text = ofsetX.toString();
                      setState(() {});
                    }
                  },
                  onLongPressStart: isLongPress
                      ? null
                      : (_) {
                          isLongPress = true;
                          // 开始长按时启动定时器
                          _timer = Timer.periodic(Duration(milliseconds: 100), (_) {
                            if (ofsetX > -5) {
                              ofsetX = double.parse((ofsetX - 0.1).toStringAsFixed(1));
                              ofsetXController?.text = ofsetX.toString();
                              setState(() {});
                            }
                          });
                        },
                  onLongPressEnd: (_) {
                    isLongPress = false;
                    // 结束长按时取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  onLongPressCancel: () {
                    isLongPress = false;
                    // 如果用户在长按时取消了长按，也需要取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    color: Colors.transparent,
                    child: SvgIcon(
                      'assets/arrow_left.svg',
                      height: 15,
                      fit: BoxFit.cover,
                      color: ofsetX == -5 ? Color(0xFFF6F6F6) : Color(0xFF262626),
                      matchTextDirection: true,
                    ),
                  ),
                ),
                Container(
                  width: 53,
                  height: 32,
                  alignment: Alignment.center,
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 3),
                  child: KeyboardActions(
                    config: _buildConfig(),
                    child: CupertinoTextField(
                      controller: ofsetXController,
                      focusNode: nodeX,
                      textInputAction: TextInputAction.done,
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.ltr,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^[-]?\d*\.?\d{0,1}$')), // 只允许输入数字和一个小数点
                      ],
                      scrollPadding: EdgeInsets.all(0),
                      keyboardType: Platform.isIOS
                          ? TextInputType.datetime
                          : const TextInputType.numberWithOptions(signed: false, decimal: true),
                      style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                      decoration: BoxDecoration(
                        color: Color(0x14747480), // 设置背景色
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      onChanged: (value) {
                        var result = value;
                        var offset = double.parse(value);
                        if (offset > 5) {
                          result = "5";
                        }
                        if (offset < -5) {
                          result = "-5";
                        }
                        ofsetXController?.text = result;
                        ofsetX = double.parse(result);
                      },
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    if (ofsetX < 5) {
                      ofsetX = double.parse((ofsetX + 0.1).toStringAsFixed(1));
                      ofsetXController?.text = ofsetX.toString();
                      setState(() {});
                    }
                  },
                  onLongPressStart: isLongPress
                      ? null
                      : (_) {
                          isLongPress = true;
                          // 开始长按时启动定时器
                          _timer = Timer.periodic(Duration(milliseconds: 100), (_) {
                            if (ofsetX < 5) {
                              ofsetX = double.parse((ofsetX + 0.1).toStringAsFixed(1));
                              ofsetXController?.text = ofsetX.toString();
                              setState(() {});
                            }
                          });
                        },
                  onLongPressEnd: (_) {
                    isLongPress = false;
                    // 结束长按时取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  onLongPressCancel: () {
                    isLongPress = false;
                    // 如果用户在长按时取消了长按，也需要取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    color: Colors.transparent,
                    child: SvgIcon(
                      'assets/arrow_right.svg',
                      height: 15,
                      color: ofsetX == 5 ? Color(0xFFF6F6F6) : Color(0xFF262626),
                      fit: BoxFit.cover,
                      matchTextDirection: true,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsetsDirectional.only(start: 12, top: 10, bottom: 10),
            child: Divider(
              color: Color(0xFFEBEBEB),
              height: 1,
            ),
          ),
          Container(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Text(
                  widget.logic.getI18nString('app100001511', '垂直偏移(mm)'),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                ),
                Expanded(child: Container()),
                GestureDetector(
                  onTap: () {
                    if (ofsetY > -5) {
                      ofsetY = double.parse((ofsetY - 0.1).toStringAsFixed(1));
                      ofsetYController?.text = ofsetY.toString();
                      setState(() {});
                    }
                  },
                  onLongPressStart: isLongPress
                      ? null
                      : (_) {
                          isLongPress = true;
                          _timer = Timer.periodic(Duration(milliseconds: 100), (_) {
                            if (ofsetY > -5) {
                              ofsetY = double.parse((ofsetY - 0.1).toStringAsFixed(1));
                              ofsetYController?.text = ofsetY.toString();
                              setState(() {});
                            }
                          });
                        },
                  onLongPressEnd: (_) {
                    isLongPress = false;
                    // 结束长按时取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  onLongPressCancel: () {
                    isLongPress = false;
                    // 如果用户在长按时取消了长按，也需要取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    color: Colors.transparent,
                    child: SvgIcon(
                      'assets/arrow_top.svg',
                      height: 15,
                      color: ofsetY == -5 ? Color(0xFFF6F6F6) : Color(0xFF262626),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Container(
                  width: 50,
                  height: 32,
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 3),
                  alignment: Alignment.center,
                  child: KeyboardActions(
                    config: _buildConfig(),
                    child: CupertinoTextField(
                      controller: ofsetYController,
                      focusNode: nodeY,
                      textInputAction: TextInputAction.done,
                      textDirection: TextDirection.ltr,
                      textAlign: TextAlign.center,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^[-]?\d*\.?\d{0,1}$')), // 只允许输入数字和一个小数点
                      ],
                      scrollPadding: EdgeInsets.all(0),
                      keyboardType: Platform.isIOS
                          ? TextInputType.datetime
                          : const TextInputType.numberWithOptions(signed: false, decimal: true),
                      style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                      decoration: BoxDecoration(
                        color: Color(0x14747480), // 设置背景色
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      onChanged: (value) {
                        var result = value;
                        var offset = double.parse(value);
                        if (offset > 5) {
                          result = "5";
                        }
                        if (offset < -5) {
                          result = "-5";
                        }
                        ofsetYController?.text = result;
                        ofsetY = double.parse(result);
                      },
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    if (ofsetY < 5) {
                      ofsetY = double.parse((ofsetY + 0.1).toStringAsFixed(1));
                      ofsetYController?.text = ofsetY.toString();
                      setState(() {});
                    }
                  },
                  onLongPressStart: isLongPress
                      ? null
                      : (_) {
                          isLongPress = true;
                          // 开始长按时启动定时器
                          _timer = Timer.periodic(Duration(milliseconds: 100), (_) {
                            if (ofsetY < 5) {
                              ofsetY = double.parse((ofsetY + 0.1).toStringAsFixed(1));
                              ofsetYController?.text = ofsetY.toString();
                              setState(() {});
                            }
                          });
                        },
                  onLongPressEnd: (_) {
                    // 结束长按时取消定时器
                    isLongPress = false;
                    _timer?.cancel();
                    setState(() {});
                  },
                  onLongPressCancel: () {
                    isLongPress = false;
                    // 如果用户在长按时取消了长按，也需要取消定时器
                    _timer?.cancel();
                    setState(() {});
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    color: Colors.transparent,
                    child: SvgIcon(
                      'assets/arrow_down.svg',
                      height: 15,
                      color: ofsetY == 5 ? Color(0xFFF6F6F6) : Color(0xFF262626),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

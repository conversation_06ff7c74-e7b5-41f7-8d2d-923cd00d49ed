import 'package:collection/collection.dart';

enum UpdateFieldManager {
  /// TitleWidget
  title('title'),

  /// PreviewWidget
  previewCount('previewCount'),
  preview('preview'),
  previewImage('previewImage'),

  ///DeviceWidget
  device('device'),

  ///SettingWidget
  setting("setting"),
  density("density"),
  settingCount("settingCount"),

  ///BottomWidget
  printButton("printButton");

  final String value;

  const UpdateFieldManager(this.value);

  static UpdateFieldManager? byValue(String value) {
    return UpdateFieldManager.values.firstWhereOrNull((e) => e.value == value);
  }
}

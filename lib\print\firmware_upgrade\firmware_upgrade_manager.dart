import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:nety/models/niimbot_firmware.dart';
import 'package:nety/models/niimbot_firmware_upgrade_info.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/nety.dart';
import 'package:nety/store.dart';
import 'package:niimbot_print_setting_plugin/extensions/change_notifer.dart';

class FirmwareUpgradeManager {
  static FirmwareUpgradeManager? _instance;
  FirmwareUpgradeManager._();
  static bool isLastUpgradeSuccess = false;

  factory FirmwareUpgradeManager() {
    _instance ??= FirmwareUpgradeManager._();
    return _instance!;
  }

  BasicMessageChannel<String> messageChannel =
      const BasicMessageChannel<String>('com.niimbot.printer/basicMessage/channel', StringCodec());

  sendMessageToNative(String message) {
    messageChannel.send(message);
  }

  /// 固件升级
  //【data】固件文件数据流
  //【version】固件版本号
  //【crc】文件MD5
  printerFirmUpgrade(Map<String, dynamic> upgradeInfp) async {
    if (Platform.isAndroid) {
      upgradeInfp["data"] = await loadLocalFileBytes(upgradeInfp["data"]);
    }

    NiimbotFirmware firmware =
        NiimbotFirmware(data: upgradeInfp["data"], version: upgradeInfp["version"], CRC: upgradeInfp["crc"]);
    NiimbotPrintSDK().upgradeFirmware(firmware);
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, NiimbotPrinter?>((v) => v.connectedPrinter, (oldVal, newVal) {
      if (needRefreshUpgradeState(oldVal, newVal)) {
        Map<String, dynamic> upgradeInfo;
        /// 升级成功和升级失败时都会提示升级失败，升级成功时客户端无需弹连接断开
      if (newVal == null && !isLastUpgradeSuccess) {
          NiimbotFirmwareUpgradeInfo firmwareUpgradeInfo = const NiimbotFirmwareUpgradeInfo(progress: 0, errorCode: 1);
          upgradeInfo = firmwareUpgradeInfo.toJson() ?? {};
        } else {
          upgradeInfo = newVal?.firmwareUpgradeInfo!.toJson() ?? {};
          if (newVal?.firmwareUpgradeInfo!.errorCode == 0) {
            isLastUpgradeSuccess = newVal?.firmwareUpgradeInfo!.progress == 100;
          }
        }
        Map upgradeMessage = {"actionName": "printerFirmwareUpgrade", "printerFirmwareUpgrade": upgradeInfo};
        sendMessageToNative(jsonEncode(upgradeMessage));
        debugPrint(
            "升级旧值：${oldVal?.firmwareUpgradeInfo?.toJson() ?? {}}  升级新值：${newVal?.firmwareUpgradeInfo!.toJson() ?? {}}");
      }
    });
  }

  needRefreshUpgradeState(NiimbotPrinter? oldVal, NiimbotPrinter? newVal) {
    if (oldVal == null) {
      return false;
    }
    if (newVal == null) {
      return true;
    } else {
      int oldProgress = oldVal.firmwareUpgradeInfo?.progress ?? 0;
      int oldErrorCode = oldVal.firmwareUpgradeInfo?.errorCode ?? 0;
      int newProgress = newVal.firmwareUpgradeInfo?.progress ?? 0;
      int newErrorCode = newVal.firmwareUpgradeInfo?.errorCode ?? 0;
      if (oldProgress != newProgress || oldErrorCode != newErrorCode) {
        return true;
      } else {
        return false;
      }
    }
  }

  Future<Uint8List> loadLocalFileBytes(String filePath) async {
    try {
      // 创建 File 对象
      final file = File(filePath);

      // 检查文件是否存在
      if (!await file.exists()) {
        print("File does not exist: $filePath");
        return Uint8List(0);
      }

      // 读取文件内容并返回字节数组
      final bytes = await file.readAsBytes();
      return bytes;
    } catch (e) {
      print("Error loading file: $e");
      return Uint8List(0);
    }
  }

  cancelPrinterUpgrade() {
    // NiimbotPrintSDK().stopPrinterUpgrade();
  }

  receiveMessagesFromNative() {
    messageChannel.setMessageHandler((String? message) async {
      print("Received from native: $message");
      // 可以在这里处理接收到的消息
      return 'Response from Flutter';
    });
  }
}
